from rest_framework.throttling import UserRateThrottle, AnonRateThrottle

class LoginRateThrottle(AnonRateThrottle):
    """
    Throttle login attempts based on IP address
    """
    scope = 'login'


class PasswordResetThrottle(AnonRateThrottle):
    """
    Throttle password reset requests based on IP address
    """
    scope = 'password_reset'


class PasswordChangeThrottle(UserRateThrottle):
    """
    Throttle password change requests for authenticated users
    """
    scope = 'password_change'


class FailedLoginThrottle(AnonRateThrottle):
    """
    Stricter throttling for failed login attempts
    """
    scope = 'failed_login'

    def get_cache_key(self, request, view):
        if request.user and getattr(request.user, 'is_authenticated', False):
            ident = request.user.pk
        else:
            ident = self.get_ident(request)

        # Safely get email from request data
        email = getattr(request, 'data', {}).get('email', '')
        if email:
            return self.cache_format % {
                'scope': self.scope,
                'ident': f"{ident}:{email}"
            }

        return self.cache_format % {
            'scope': self.scope,
            'ident': ident
        }
