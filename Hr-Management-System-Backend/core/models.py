from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager,PermissionsMixin
from django.utils import timezone
from core.fields import EncryptedField
from profiles.models import *


# -----------------------------
# Custom User Manager
# -----------------------------
class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError("Email is required")
        if 'role' not in extra_fields or not extra_fields['role']:
            raise ValueError("Role is required")

        user = self.model(
            email=self.normalize_email(email),
            **extra_fields
        )
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        Superuser is treated as HR with access_level Hr3.
        """
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_staff", True)

        # Import here to avoid circular import
        from .models import Organization, Role

        # Ensure an organization exists
        if 'organization' not in extra_fields or not extra_fields['organization']:
            organization, _ = Organization.objects.get_or_create(
                company_name="Default Organization",
                defaults={'industry_type': 'Technology'}
            )
            extra_fields['organization'] = organization

        # Ensure an HR role exists
        if 'role' not in extra_fields or not extra_fields['role']:
            hr_role, _ = Role.objects.get_or_create(
                organization=extra_fields['organization'],
                role_name="HR",
                defaults={'description': 'Human Resources'}
            )
            extra_fields['role'] = hr_role

        # Ensure superuser is always Hr3 (highest HR access)
        extra_fields['access_level'] = "Hr3"

        return self.create_user(email, password, **extra_fields)


# -----------------------------
# Organization
# -----------------------------
class Organization(models.Model):
    company_name = models.CharField(max_length=150, unique=True)
    industry_type = models.CharField(max_length=100, blank=True)
    email_domain = models.CharField(max_length=100, help_text="Domain for company emails (e.g., company.com)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.company_name


# -----------------------------
# Role
# -----------------------------
class Role(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE,null=False,blank=False)
    role_name = models.CharField(max_length=50)  # only "Employee" or "HR"
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.role_name


# -----------------------------
# Permission
# -----------------------------
class Permission(models.Model):
    permission_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


# -----------------------------
# RolePermission Mapping
# -----------------------------
class RolePermission(models.Model):
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('role', 'permission')

    def __str__(self):
        return f"{self.role.role_name} - {self.permission.permission_name}"


# -----------------------------
# Department
# -----------------------------
class Department(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    department_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.department_name} - {self.organization.company_name}"

    class Meta:
        ordering = ['department_name']


# -----------------------------
# Custom User Model
# -----------------------------
class User(AbstractBaseUser,PermissionsMixin):
    ACCESS_LEVELS = [
        ("Emp1", 'Basic'),
        ("Emp2", 'Team Member'),
        ("Emp3", 'Team Lead'),
        ("Emp4", 'Department Head'),
        ("Hr1", 'HR Executive'),
        ("Hr2", 'HR Manager'),
        ("Hr3", 'HR Director'),  # Highest access
    ]

    access_level = models.CharField(max_length=20, choices=ACCESS_LEVELS, default='Emp1')
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True)
    role = models.ForeignKey(Role, on_delete=models.CASCADE, null=True, blank=True)
    designation = models.CharField(max_length=100, null=True, blank=True)
    date_of_joining = models.DateField(null=True, blank=True)
    work_location = models.CharField(max_length=100, null=True, blank=True)
    employment_type = models.CharField(max_length=50, null=True, blank=True)
    previous_company_name = models.CharField(max_length=100, null=True, blank=True)
    years_of_experience = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    highest_qualification = models.CharField(max_length=100, null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    
    
    email = models.EmailField(unique=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)  # For Django admin panel
    is_superuser = models.BooleanField(default=False)  # For Django admin panel
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    def __str__(self):
            return self.email

    def is_hr3(self):
        """Check if user is HR with top-level Hr3 access."""
        return (
            self.role
            and self.role.role_name == "HR"
            and self.access_level == "Hr3"
        )

    def has_permission(self, perm_name):
        if self.role:
            return self.role.rolepermission_set.filter(permission__permission_name=perm_name).exists()
        return False

    def has_perm(self, perm, obj=None):
        # Hr3 and Django superusers have all perms
        if self.is_superuser or self.is_hr3():
            return True
        return self.has_permission(perm)

    def has_module_perms(self, app_label):
        if self.is_superuser or self.is_hr3():
            return True
        return self.is_staff

    def get_full_name(self):
        """Return the full name for the user via Profile relation if exists."""
        if hasattr(self, 'profile') and self.profile:
            return f"{self.profile.first_name} {self.profile.last_name}".strip()
        return self.email

    def get_short_name(self):
        """Return the short name for the user."""
        if hasattr(self, 'profile') and self.profile and self.profile.first_name:
            return self.profile.first_name
        return self.email
