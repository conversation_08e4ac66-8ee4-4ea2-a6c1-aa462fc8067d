from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core import mail
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from core.views import *
from unittest.mock import patch
from rest_framework import status
from rest_framework.authtoken.models import Token
from core.models import Department, Organization, Role

User = get_user_model()

@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class RegisterViewTest(APITestCase):
    """Test cases for RegisterView"""

    def setUp(self):
        self.url = reverse('register')

        # Create test organization
        self.organization = Organization.objects.create(
            company_name='Test Company',
            industry_type='Technology'
        )

        # Create test role
        self.role = Role.objects.create(
            role_name='Test Role',
            organization=self.organization
        )

        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'confirm_password': 'TestPass123!',
            'organization': self.organization.id,
            'role': self.role.id,
            'access_level': 'Emp1',
            'designation': 'Software Engineer',
            'date_of_joining': '2023-01-01',
            'work_location': 'Test Location',
            'employment_type': 'Full-time'
        }

    def test_register_success(self):
        """Test successful user registration"""
        response = self.client.post(self.url, self.valid_data)
        if response.status_code != status.HTTP_201_CREATED:
            print("Response status:", response.status_code)
            print("Response data:", response.data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check user was created
        user = User.objects.get(email=self.valid_data['email'])
    # self.assertEqual(user.username, self.valid_data['username'])

    def test_register_duplicate_email(self):
        """Test registration with duplicate email"""
        # Create user with required fields for User model
        user_data = {
            'email': self.valid_data['email'],
            'password': self.valid_data['password'],
            'organization': self.organization,
            'role': self.role,
            'access_level': self.valid_data['access_level']
        }
        User.objects.create_user(**user_data)
        response = self.client.post(self.url, self.valid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_register_invalid_data(self):
        """Test registration with invalid data"""
        invalid_data = {'email': 'invalid-email'}
        response = self.client.post(self.url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class CustomTokenObtainPairViewTest(APITestCase):
    """Test cases for CustomTokenObtainPairView (Login)"""

    def setUp(self):
        self.url = reverse('token_obtain_pair')
        self.user = User.objects.create_user(
            email='<EMAIL>',
                # username removed
            password='testpass123'
        )

    def test_login_success(self):
        """Test successful login"""
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_missing_email(self):
        """Test login with missing email"""
        data = {'password': 'testpass123'}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class LogoutViewTest(APITestCase):
    """Test cases for LogoutView"""

    def setUp(self):
        self.url = reverse('logout')
        self.user = User.objects.create_user(
            email='<EMAIL>',
                # username removed
            password='testpass123'
        )
        self.refresh_token = RefreshToken.for_user(self.user)

    def test_logout_success(self):
        """Test successful logout"""
        self.client.force_authenticate(user=self.user)
        data = {'refresh': str(self.refresh_token)}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_205_RESET_CONTENT)

    def test_logout_unauthenticated(self):
        """Test logout without authentication"""
        data = {'refresh': str(self.refresh_token)}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_logout_invalid_token(self):
        """Test logout with invalid refresh token"""
        self.client.force_authenticate(user=self.user)
        data = {'refresh': 'invalid_token'}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class ForgotPasswordViewTest(APITestCase):
    """Test cases for ForgotPasswordView"""

    def setUp(self):
        self.url = reverse('forgot_password')
        self.user = User.objects.create_user(
            email='<EMAIL>',
                # username removed
            password='testpass123'
        )

    @patch('accounts.views.send_mail')
    def test_forgot_password_success(self, mock_send_mail):
        """Test successful password reset request"""
        mock_send_mail.return_value = True
        data = {'email': '<EMAIL>'}
        response = self.client.post(self.url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('Password reset link sent', response.data['message'])
        mock_send_mail.assert_called_once()

    def test_forgot_password_user_not_found(self):
        """Test password reset for non-existent user"""
        data = {'email': '<EMAIL>'}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_forgot_password_missing_email(self):
        """Test password reset without email"""
        response = self.client.post(self.url, {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class ChangePasswordViewTest(APITestCase):
    """Test cases for ChangePasswordView"""

    def setUp(self):
        self.url = reverse('change_password')
        self.user = User.objects.create_user(
            email='<EMAIL>',
                # username removed
            password='oldpass123'
        )

    def test_change_password_success(self):
        """Test successful password change"""
        self.client.force_authenticate(user=self.user)
        data = {
            'current_password': 'oldpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        }
        response = self.client.post(self.url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewPass123!'))

    def test_change_password_unauthenticated(self):
        """Test password change without authentication"""
        data = {
            'current_password': 'oldpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_change_password_wrong_current(self):
        """Test password change with wrong current password"""
        self.client.force_authenticate(user=self.user)
        data = {
            'current_password': 'wrongpass',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_change_password_mismatch(self):
        """Test password change with mismatched passwords"""
        self.client.force_authenticate(user=self.user)
        data = {
            'current_password': 'oldpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'DifferentPass123!'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_change_password_missing_fields(self):
        """Test password change with missing fields"""
        self.client.force_authenticate(user=self.user)
        data = {'current_password': 'oldpass123'}  # Missing other fields
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TokenFunctionsTest(TestCase):
    """Test cases for token utility functions"""

    def setUp(self):
        self.email = '<EMAIL>'

    def test_generate_reset_token(self):
        """Test generating reset token"""
        token = generate_reset_token(self.email)
        self.assertIsInstance(token, str)
        self.assertTrue(len(token) > 0)

    def test_verify_reset_token_valid(self):
        """Test verifying valid token"""
        token = generate_reset_token(self.email)
        verified_email = verify_reset_token(token, expiration=600)
        self.assertEqual(verified_email, self.email)

    def test_verify_reset_token_invalid(self):
        """Test verifying invalid token"""
        verified_email = verify_reset_token('invalid_token')
        self.assertIsNone(verified_email)

    def test_verify_reset_token_expired(self):
        """Test verifying expired token"""
        import time
        token = generate_reset_token(self.email)
        time.sleep(1)  # Wait a bit
        verified_email = verify_reset_token(token, expiration=0)  # Immediate expiry
        self.assertIsNone(verified_email)
class RoleAPITestCase(APITestCase):
    def setUp(self):
        self.role = Role.objects.create(role_name="Manager", description="Team manager")
        self.role_data = {"role_name": "Admin", "description": "Admin role"}

    def test_list_roles(self):
        response = self.client.get("/roles/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_role(self):
        response = self.client.post("/roles/", data=self.role_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["role_name"], "Admin")

    def test_retrieve_role(self):
        response = self.client.get(f"/roles/{self.role.id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["role_name"], "Manager")

    def test_update_role(self):
        update_data = {"role_name": "Lead", "description": "Updated"}
        response = self.client.put(f"/roles/{self.role.id}/", data=update_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["role_name"], "Lead")

    def test_delete_role(self):
        response = self.client.delete(f"/roles/{self.role.id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)


class PermissionAPITestCase(APITestCase):
    def setUp(self):
        self.permission = Permission.objects.create(code_name="edit_user", description="Can edit user")
        self.permission_data = {"code_name": "view_user", "description": "Can view user"}

    def test_list_permissions(self):
        response = self.client.get("/permissions/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_permission(self):
        response = self.client.post("/permissions/", data=self.permission_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["code_name"], "view_user")

    def test_retrieve_permission(self):
        response = self.client.get(f"/permissions/{self.permission.id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["code_name"], "edit_user")

    def test_update_permission(self):
        update_data = {"code_name": "manage_user", "description": "Updated"}
        response = self.client.put(f"/permissions/{self.permission.id}/", data=update_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["code_name"], "manage_user")

    def test_delete_permission(self):
        response = self.client.delete(f"/permissions/{self.permission.id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)


class RolePermissionAPITestCase(APITestCase):
    def setUp(self):
        self.role = Role.objects.create(role_name="Editor", description="Content editor")
        self.permission = Permission.objects.create(code_name="edit_post", description="Can edit post")
        self.role_permission = RolePermission.objects.create(role=self.role, permission=self.permission)

    def test_list_role_permissions(self):
        response = self.client.get("/role-permissions/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_role_permission(self):
        new_role = Role.objects.create(role_name="Reviewer", description="Review content")
        new_permission = Permission.objects.create(code_name="review_post", description="Can review post")
        data = {"role": new_role.id, "permission": new_permission.id}
        response = self.client.post("/role-permissions/", data=data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_retrieve_role_permission(self):
        response = self.client.get(f"/role-permissions/{self.role_permission.id}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_role_permission(self):
        new_permission = Permission.objects.create(code_name="delete_post", description="Can delete post")
        data = {"role": self.role.id, "permission": new_permission.id}
        response = self.client.put(f"/role-permissions/{self.role_permission.id}/", data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["permission"], new_permission.id)

    def test_delete_role_permission(self):
        response = self.client.delete(f"/role-permissions/{self.role_permission.id}/")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

class DepartmentAPITest(APITestCase):
    def setUp(self):
        self.organization = Organization.objects.create(name="Test Org")

        self.hr_director_role = Role.objects.create(role_name="HR Director")
        self.hr_exec_role = Role.objects.create(role_name="HR Executive")

        self.hr_director = User.objects.create_user(
            # username removed
            password='password123',
            role=self.hr_director_role,
            organization=self.organization
        )

        self.hr_exec = User.objects.create_user(
            # username removed
            password='password123',
            role=self.hr_exec_role,
            organization=self.organization
        )

        self.director_token = Token.objects.create(user=self.hr_director)
        self.exec_token = Token.objects.create(user=self.hr_exec)

        self.list_url = '/departments/'  # Make sure this matches your urls.py

    def test_get_departments_with_auth(self):
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.director_token.key)
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_department_as_hr_director(self):
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.director_token.key)
        data = {
            "department_name": "Finance",
            "description": "Handles finance",
            "organization": self.organization.id
        }
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_department_as_hr_exec_denied(self):
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.exec_token.key)
        data = {
            "department_name": "Marketing",
            "description": "Handles marketing",
            "organization": self.organization.id
        }
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_access_denied(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_update_department_as_hr_director(self):
        department = Department.objects.create(
            department_name="HR",
            description="Handles people",
            organization=self.organization
        )
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.director_token.key)
        url = f'/departments/{department.id}/'
        data = {"department_name": "Human Resources", "description": "Updated", "organization": self.organization.id}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['department_name'], "Human Resources")

    def test_delete_department_as_hr_exec_denied(self):
        department = Department.objects.create(
            department_name="Legal",
            description="Handles legal",
            organization=self.organization
        )
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.exec_token.key)
        url = f'/departments/{department.id}/'
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)