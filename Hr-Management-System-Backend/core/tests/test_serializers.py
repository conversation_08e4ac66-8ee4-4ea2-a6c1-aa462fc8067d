from django.test import TestCase
from django.contrib.auth import get_user_model
from accounts.serializers import RegisterSerializer, UserSerializer

User = get_user_model()


class RegisterSerializerTest(TestCase):
    """Test cases for RegisterSerializer"""

    def setUp(self):
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }

    def test_valid_registration_data(self):
        """Test serializer with valid registration data"""
        serializer = RegisterSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())

    def test_missing_email(self):
        """Test serializer with missing email"""
        data = self.valid_data.copy()
        del data['email']
        serializer = RegisterSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('email', serializer.errors)

    # Username is not required anymore, so skip this test

    def test_missing_password(self):
        """Test serializer with missing password"""
        data = self.valid_data.copy()
        del data['password']
        serializer = RegisterSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('password', serializer.errors)

    def test_invalid_email_format(self):
        """Test serializer with invalid email format"""
        data = self.valid_data.copy()
        data['email'] = 'invalid-email'
        serializer = RegisterSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('email', serializer.errors)

    def test_create_user(self):
        """Test creating user through serializer"""
        serializer = RegisterSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
        user = serializer.save()
        
        self.assertIsInstance(user, User)
        self.assertEqual(user.email, self.valid_data['email'])
        self.assertTrue(user.check_password(self.valid_data['password']))

    def test_password_write_only(self):
        """Test that password field is write-only"""
        serializer = RegisterSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
        user = serializer.save()
        
        # Serialize the created user
        serialized_data = RegisterSerializer(user).data
        self.assertNotIn('password', serialized_data)


class UserSerializerTest(TestCase):
    """Test cases for UserSerializer"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_user_serialization(self):
        """Test serializing a user"""
        serializer = UserSerializer(self.user)
        data = serializer.data
        
        expected_fields = [
            'id', 'email', 'is_active', 
            'last_login', 'created_at', 'updated_at'
        ]
        
        for field in expected_fields:
            self.assertIn(field, data)
        
        self.assertEqual(data['email'], self.user.email)
        self.assertEqual(data['is_active'], self.user.is_active)

    def test_password_not_in_serialized_data(self):
        """Test that password is not included in serialized data"""
        serializer = UserSerializer(self.user)
        data = serializer.data
        self.assertNotIn('password', data)

    def test_sensitive_fields_not_included(self):
        """Test that sensitive fields are not included"""
        serializer = UserSerializer(self.user)
        data = serializer.data
        
        sensitive_fields = ['is_staff', 'is_superuser']
        for field in sensitive_fields:
            self.assertNotIn(field, data)
