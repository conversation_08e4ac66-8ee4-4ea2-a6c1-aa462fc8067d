from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from rest_framework.test import APITestCase, APIRequestFactory
from rest_framework.views import APIView
from accounts.throttles import (
    LoginRateThrottle,
    PasswordResetThrottle,
    PasswordChangeThrottle,
    FailedLoginThrottle
)

User = get_user_model()

class MockView(APIView):
    """Mock view for testing throttles"""
    pass

class LoginRateThrottleTest(TestCase):
    """Test cases for LoginRateThrottle"""

    def setUp(self):
        self.factory = APIRequestFactory()
        self.throttle = LoginRateThrottle()
        self.view = MockView()

    def test_throttle_scope(self):
        """Test that throttle has correct scope"""
        self.assertEqual(self.throttle.scope, 'login')

    def test_throttle_inheritance(self):
        """Test that throttle inherits from AnonRateThrottle"""
        from rest_framework.throttling import AnonRateThrottle
        self.assertIsInstance(self.throttle, AnonRateThrottle)

    def test_get_cache_key(self):
        """Test cache key generation"""
        request = self.factory.post('/login/')
        request.user = AnonymousUser()
        
        cache_key = self.throttle.get_cache_key(request, self.view)
        self.assertIsNotNone(cache_key)
        self.assertIn('login', cache_key)

class PasswordResetThrottleTest(TestCase):
    """Test cases for PasswordResetThrottle"""

    def setUp(self):
        self.factory = APIRequestFactory()
        self.throttle = PasswordResetThrottle()
        self.view = MockView()

    def test_throttle_scope(self):
        """Test that throttle has correct scope"""
        self.assertEqual(self.throttle.scope, 'password_reset')

    def test_throttle_inheritance(self):
        """Test that throttle inherits from AnonRateThrottle"""
        from rest_framework.throttling import AnonRateThrottle
        self.assertIsInstance(self.throttle, AnonRateThrottle)

class PasswordChangeThrottleTest(TestCase):
    """Test cases for PasswordChangeThrottle"""

    def setUp(self):
        self.factory = APIRequestFactory()
        self.throttle = PasswordChangeThrottle()
        self.view = MockView()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_throttle_scope(self):
        """Test that throttle has correct scope"""
        self.assertEqual(self.throttle.scope, 'password_change')

    def test_throttle_inheritance(self):
        """Test that throttle inherits from UserRateThrottle"""
        from rest_framework.throttling import UserRateThrottle
        self.assertIsInstance(self.throttle, UserRateThrottle)

    def test_get_cache_key_authenticated_user(self):
        """Test cache key generation for authenticated user"""
        request = self.factory.post('/change-password/')
        request.user = self.user
        
        cache_key = self.throttle.get_cache_key(request, self.view)
        self.assertIsNotNone(cache_key)
        self.assertIn('password_change', cache_key)
