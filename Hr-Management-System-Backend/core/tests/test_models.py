from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.db import IntegrityError  # Add this import
from core.models import Organization, Department, Role, Permission, RolePermission

User = get_user_model()

class CustomUserManagerTest(TestCase):
    """Test cases for CustomUserManager"""

    def setUp(self):
        self.manager = CustomUserManager()
        self.manager.model = User

    def test_create_user_success(self):
        """Test creating a regular user successfully"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_user_without_email(self):
        """Test creating user without email raises ValueError"""
        with self.assertRaises(ValueError) as context:
            User.objects.create_user(
                email='',
                password='testpass123'
            )
        self.assertEqual(str(context.exception), "Email is required")

    def test_create_user_normalizes_email(self):
        """Test that email is normalized when creating user"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')

    def test_create_superuser_success(self):
        """Test creating a superuser successfully"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)

    def test_create_superuser_with_extra_fields(self):
        """Test creating superuser with additional fields"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123',
            is_active=True
        )
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)

class UserModelTest(TestCase):
    """Test cases for User model"""

    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }

    def test_user_creation(self):
        """Test basic user creation"""
        user = User.objects.create_user(**self.user_data)
        self.assertIsInstance(user, User)
        self.assertEqual(user.email, self.user_data['email'])
    self.assertEqual(user.email, self.user_data['email'])

    def test_user_str_method(self):
        """Test user string representation"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), self.user_data['email'])

    def test_email_unique_constraint(self):
        """Test that email must be unique"""
        User.objects.create_user(**self.user_data)
        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                email='<EMAIL>',  # Same email
                password='password123'
            )

    # Username uniqueness test removed since username is not used

    def test_user_fields_default_values(self):
        """Test default values for user fields"""
        user = User.objects.create_user(**self.user_data)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertIsNotNone(user.created_at)
        self.assertIsNotNone(user.updated_at)
        self.assertIsNone(user.last_login)

    def test_username_field_setting(self):
        """Test that USERNAME_FIELD is set to email"""
        self.assertEqual(User.USERNAME_FIELD, 'email')

    def test_required_fields_setting(self):
        """Test that REQUIRED_FIELDS is empty since username is not used"""
        self.assertEqual(User.REQUIRED_FIELDS, [])
    def test_user_profile_relationship(self):
        """Test user-profile relationship"""
        user = User.objects.create_user(**self.user_data)

        # Initially user should not have a profile
        self.assertFalse(hasattr(user, 'profile') and user.profile)

        # Test that we can check for profile existence
        try:
            profile = user.profile
            self.fail("Should raise RelatedObjectDoesNotExist")
        except:
            pass  # Expected behavior

    def test_user_access_level_choices(self):
        """Test user access level choices"""
        user = User.objects.create_user(**self.user_data)

        # Test default access level
        self.assertEqual(user.access_level, 'Emp1')

        # Test setting different access levels
        for level_code, level_name in User.ACCESS_LEVELS:
            user.access_level = level_code
            user.save()
            user.refresh_from_db()
            self.assertEqual(user.access_level, level_code)

    def test_user_has_permission_method(self):
        """Test user has_permission method"""
        user = User.objects.create_user(**self.user_data)

        # User without role should not have permissions
        result = user.has_permission('test_permission')
        self.assertFalse(result)

    def test_user_has_perm_method(self):
        """Test user has_perm method for Django admin compatibility"""
        user = User.objects.create_user(**self.user_data)

        # Regular user should not have permissions
        self.assertFalse(user.has_perm('test_permission'))

        # Superuser should have all permissions
        user.is_superuser = True
        self.assertTrue(user.has_perm('test_permission'))

    def test_user_has_module_perms_method(self):
        """Test user has_module_perms method for Django admin compatibility"""
        user = User.objects.create_user(**self.user_data)

        # Regular user without staff status should not have module permissions
        self.assertFalse(user.has_module_perms('test_app'))

        # Superuser should have all module permissions
        user.is_superuser = True
        self.assertTrue(user.has_module_perms('test_app'))

        # Staff user should have module permissions
        user.is_superuser = False
        user.is_staff = True
        self.assertTrue(user.has_module_perms('test_app'))


class OrganizationModelTest(TestCase):
    """Test cases for Organization model"""

    def test_organization_creation(self):
        """Test basic organization creation"""
        org = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        self.assertIsInstance(org, Organization)
        self.assertEqual(org.company_name, "Test Company")
        self.assertEqual(org.industry_type, "Technology")

    def test_organization_str_method(self):
        """Test organization string representation"""
        org = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        self.assertEqual(str(org), "Test Company")

    def test_organization_unique_company_name(self):
        """Test that company name must be unique"""
        Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )

        with self.assertRaises(IntegrityError):
            Organization.objects.create(
                company_name="Test Company",  # Same name
                industry_type="Finance"
            )

    def test_organization_timestamps(self):
        """Test organization timestamps"""
        org = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        self.assertIsNotNone(org.created_at)
        self.assertIsNotNone(org.updated_at)

    def test_organization_industry_type_optional(self):
        """Test that industry_type is optional"""
        org = Organization.objects.create(
            company_name="Test Company"
            # industry_type not provided
        )
        self.assertEqual(org.industry_type, "")


class DepartmentModelTest(TestCase):
    """Test cases for Department model"""

    def setUp(self):
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )

    def test_department_creation(self):
        """Test basic department creation"""
        dept = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        self.assertIsInstance(dept, Department)
        self.assertEqual(dept.department_name, "Engineering")
        self.assertEqual(dept.organization, self.organization)

    def test_department_str_method(self):
        """Test department string representation"""
        dept = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        expected_str = f"Engineering - {self.organization.company_name}"
        self.assertEqual(str(dept), expected_str)

    def test_department_cascade_delete_with_organization(self):
        """Test that department is deleted when organization is deleted"""
        dept = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        dept_id = dept.id

        self.organization.delete()

        with self.assertRaises(Department.DoesNotExist):
            Department.objects.get(id=dept_id)

    def test_department_ordering(self):
        """Test department ordering by department_name"""
        dept_b = Department.objects.create(
            organization=self.organization,
            department_name="B Department"
        )
        dept_a = Department.objects.create(
            organization=self.organization,
            department_name="A Department"
        )

        departments = list(Department.objects.all())
        self.assertEqual(departments[0], dept_a)
        self.assertEqual(departments[1], dept_b)


class RoleModelTest(TestCase):
    """Test cases for Role model"""

    def setUp(self):
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )

    def test_role_creation(self):
        """Test basic role creation"""
        role = Role.objects.create(
            organization=self.organization,
            role_name="HR Manager",
            description="Handles human resources"
        )
        self.assertIsInstance(role, Role)
        self.assertEqual(role.role_name, "HR Manager")
        self.assertEqual(role.organization, self.organization)

    def test_role_str_method(self):
        """Test role string representation"""
        role = Role.objects.create(
            organization=self.organization,
            role_name="HR Manager",
            description="Handles human resources"
        )
        self.assertEqual(str(role), "HR Manager")

    def test_role_cascade_delete_with_organization(self):
        """Test that role is deleted when organization is deleted"""
        role = Role.objects.create(
            organization=self.organization,
            role_name="HR Manager",
            description="Handles human resources"
        )
        role_id = role.id

        self.organization.delete()

        with self.assertRaises(Role.DoesNotExist):
            Role.objects.get(id=role_id)


class PermissionModelTest(TestCase):
    """Test cases for Permission model"""

    def test_permission_creation(self):
        """Test basic permission creation"""
        permission = Permission.objects.create(
            permission_name="view_employee_data",
            description="Can view employee data"
        )
        self.assertIsInstance(permission, Permission)
        self.assertEqual(permission.permission_name, "view_employee_data")

    def test_permission_unique_name(self):
        """Test that permission name must be unique"""
        Permission.objects.create(
            permission_name="view_employee_data",
            description="Can view employee data"
        )

        with self.assertRaises(IntegrityError):
            Permission.objects.create(
                permission_name="view_employee_data",  # Same name
                description="Different description"
            )


class RolePermissionModelTest(TestCase):
    """Test cases for RolePermission model"""

    def setUp(self):
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="HR Manager",
            description="Handles human resources"
        )
        self.permission = Permission.objects.create(
            permission_name="view_employee_data",
            description="Can view employee data"
        )

    def test_role_str(self):
        """Test role string representation"""
        self.assertEqual(str(self.role), "HR Manager")

    def test_permission_str(self):
        """Test permission string representation"""
        self.assertEqual(str(self.permission), "view_employee_data")

    def test_create_role_permission(self):
        """Test creating role permission mapping"""
        role_permission = RolePermission.objects.create(
            role=self.role,
            permission=self.permission
        )
        expected_str = "HR Manager - view_employee_data"
        self.assertEqual(str(role_permission), expected_str)

    def test_unique_role_permission(self):
        """Test unique constraint on role-permission combination"""
        # First creation should work
        RolePermission.objects.create(role=self.role, permission=self.permission)

        # Second creation should raise an IntegrityError
        with self.assertRaises(IntegrityError):
            RolePermission.objects.create(role=self.role, permission=self.permission)

    def test_role_permission_cascade_delete_with_role(self):
        """Test that role permission is deleted when role is deleted"""
        role_permission = RolePermission.objects.create(
            role=self.role,
            permission=self.permission
        )
        role_permission_id = role_permission.id

        self.role.delete()

        with self.assertRaises(RolePermission.DoesNotExist):
            RolePermission.objects.get(id=role_permission_id)

    def test_role_permission_cascade_delete_with_permission(self):
        """Test that role permission is deleted when permission is deleted"""
        role_permission = RolePermission.objects.create(
            role=self.role,
            permission=self.permission
        )
        role_permission_id = role_permission.id

        self.permission.delete()

        with self.assertRaises(RolePermission.DoesNotExist):
            RolePermission.objects.get(id=role_permission_id)

    def test_role_permission_timestamps(self):
        """Test role permission timestamps"""
        role_permission = RolePermission.objects.create(
            role=self.role,
            permission=self.permission
        )
        self.assertIsNotNone(role_permission.created_at)
        self.assertIsNotNone(role_permission.updated_at)


class UserManagerWithOrganizationTest(TestCase):
    """Test cases for User creation with organization and role requirements"""

    def setUp(self):
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )

    def test_create_user_with_organization_and_role(self):
        """Test creating user with organization and role"""
        user = User.objects.create_user(
            email='<EMAIL>',
            # username removed
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        self.assertEqual(user.organization, self.organization)
        self.assertEqual(user.role, self.role)

    def test_create_user_without_organization_raises_error(self):
        """Test that creating user without organization raises ValueError"""
        with self.assertRaises(ValueError) as context:
            User.objects.create_user(
                email='<EMAIL>',
                # username removed
                password='testpass123',
                role=self.role
            )
        self.assertEqual(str(context.exception), "Organization is required")

    def test_create_user_without_role_raises_error(self):
        """Test that creating user without role raises ValueError"""
        with self.assertRaises(ValueError) as context:
            User.objects.create_user(
                email='<EMAIL>',
                # username removed
                password='testpass123',
                organization=self.organization
            )
        self.assertEqual(str(context.exception), "Role is required")

    def test_create_superuser_creates_default_organization_and_role(self):
        """Test that creating superuser creates default organization and role if not provided"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            # username removed
            password='adminpass123'
        )

        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_staff)
        self.assertIsNotNone(user.organization)
        self.assertIsNotNone(user.role)
        self.assertEqual(user.organization.company_name, "Default Organization")
        self.assertEqual(user.role.role_name, "Admin")

    def test_create_superuser_with_existing_organization_and_role(self):
        """Test creating superuser with provided organization and role"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            # username removed
            password='adminpass123',
            organization=self.organization,
            role=self.role
        )

        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_staff)
        self.assertEqual(user.organization, self.organization)
        self.assertEqual(user.role, self.role)

    def test_user_cascade_delete_with_organization(self):
        """Test that user is deleted when organization is deleted"""
        user = User.objects.create_user(
            email='<EMAIL>',
            # username removed
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        user_id = user.id

        self.organization.delete()

        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=user_id)

    def test_user_cascade_delete_with_role(self):
        """Test that user is deleted when role is deleted"""
        user = User.objects.create_user(
            email='<EMAIL>',
            # username removed
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        user_id = user.id

        self.role.delete()

        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=user_id)

    def test_role_str(self):
        self.assertEqual(str(self.role), "HR Manager")

    def test_permission_str(self):
        self.assertEqual(str(self.permission), "view_employee_data")

    def test_create_role_permission(self):
        role_permission = RolePermission.objects.create(
            role=self.role,
            permission=self.permission
        )
        expected_str = "HR Manager - view_employee_data"
        self.assertEqual(str(role_permission), expected_str)

    def test_unique_role_permission(self):
        # First creation should work
        RolePermission.objects.create(role=self.role, permission=self.permission)

        # Second creation should raise an IntegrityError
        with self.assertRaises(IntegrityError):
            RolePermission.objects.create(role=self.role, permission=self.permission)