from rest_framework.permissions import BasePermission, SAFE_METHODS

class DepartmentPermission(BasePermission):
    """
    Permission rules for department-level access:
    - Read access is allowed for all employees and HR roles.
    - Write access is restricted to HR2 and HR3 roles.
    """
    def has_permission(self, request, view):
        user = request.user

        if not user or not user.is_authenticated:
            return False  # Unauthenticated users cannot access anything

        access_level = getattr(user, 'access_level', None)
        if not access_level:
            return False  # User has no access level assigned

        # Define roles with read and write access
        read_roles = {'Emp1', 'Emp2', 'Emp3', 'Emp4', 'Hr1', 'Hr2', 'Hr3'}
        write_roles = {'Hr2', 'Hr3'}

        # Read permissions
        if request.method in SAFE_METHODS:
            return access_level in read_roles

        # Write permissions (POST, PUT, PATCH, DELETE)
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return access_level in write_roles

        return False

class IsHr3(BasePermission):
    """
    Permission rule to allow only HR3 level users to perform specific actions.
    """
    def has_permission(self, request, view):
        return bool(request.user and getattr(request.user, 'access_level', None) == "Hr3")
