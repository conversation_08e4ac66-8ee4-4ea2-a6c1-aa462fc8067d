from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import Organization, Role, User, Department
from profiles.models import Profile
from .views import *
from .models import *
from profiles.models import Profile
from profiles.serializers import ProfileSerializer
from django.contrib.auth.models import Permission as DjangoPermission

class DynamicRoleField(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        request = self.context.get('request')
        if request and hasattr(request, 'data'):
            organization_id = request.data.get('organization')
            if organization_id:
                try:
                    return Role.objects.filter(organization_id=organization_id)
                except (ValueError, TypeError):
                    return Role.objects.none()
        return Role.objects.none()

class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)
    
    # Core user fields
    organization = serializers.PrimaryKeyRelatedField(queryset=Organization.objects.all())
    role = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all())
    access_level = serializers.ChoiceField(choices=User.ACCESS_LEVELS)
    
    # Professional Info
    designation = serializers.CharField(max_length=100)
    date_of_joining = serializers.DateField()
    work_location = serializers.CharField(max_length=100)
    employment_type = serializers.CharField(max_length=50)
    department_ref = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(), 
        required=False, 
        allow_null=True
    )
    reporting_manager = serializers.PrimaryKeyRelatedField(
        queryset=Profile.objects.all(), 
        required=False, 
        allow_null=True
    )
    
    # New field for reporting manager checkbox
    is_reporting_manager = serializers.BooleanField(
        default=False,
        help_text="Check if this person will be a reporting manager for others",
        style={'base_template': 'checkbox.html'}
    )

    class Meta:
        model = User
        fields = [
            'email', 'password', 'confirm_password',
            'organization', 'role', 'access_level',
            'designation', 'date_of_joining', 'work_location', 'employment_type',
            'department_ref', 'reporting_manager', 'is_reporting_manager'
        ]
        extra_kwargs = {'password': {'write_only': True}}

    def validate(self, attrs):
        """Custom validation for the registration data"""
        # Password confirmation
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError("Password and confirm password do not match.")
        
        # Role-Organization validation
        role = attrs.get('role')
        organization = attrs.get('organization')
        if role and organization and role.organization != organization:
            raise serializers.ValidationError("Selected role must belong to the selected organization.")
        
        # Department-Organization validation
        department = attrs.get('department_ref')
        if department and organization and department.organization != organization:
            raise serializers.ValidationError("Selected department must belong to the selected organization.")
        
        # Reporting manager validation
        reporting_manager = attrs.get('reporting_manager')
        if reporting_manager and organization and reporting_manager.organization != organization:
            raise serializers.ValidationError("Reporting manager must belong to the same organization.")
        
        # If is_reporting_manager is True, they shouldn't have a reporting manager
        is_reporting_manager = attrs.get('is_reporting_manager', False)
        if is_reporting_manager and reporting_manager:
            raise serializers.ValidationError("A reporting manager cannot have another reporting manager.")
        
        return attrs

    def create(self, validated_data):
        # Remove confirm_password and is_reporting_manager as they're not needed for user creation
        validated_data.pop('confirm_password', None)
        is_reporting_manager = validated_data.pop('is_reporting_manager', False)
         # Extract professional fields for both User and Profile
        organization = validated_data.get('organization')
        designation = validated_data.get('designation')
        date_of_joining = validated_data.get('date_of_joining')
        work_location = validated_data.get('work_location')
        employment_type = validated_data.get('employment_type')
        department_ref = validated_data.pop('department_ref', None)
        reporting_manager = validated_data.pop('reporting_manager', None)
        
        # Store department in User model (field name is 'department', not 'department_ref')
        if department_ref:
            validated_data['department'] = department_ref
        
        # Create user first using create_user method to properly hash the password
        user = User.objects.create_user(**validated_data)

        # Prepare profile data
        profile_data = {
            'organization': organization,
            'designation': designation,
            'date_of_joining': date_of_joining,
            'work_location': work_location,
            'employment_type': employment_type,
            'department_ref': department_ref,
            'reporting_manager': reporting_manager,
            'email_id': validated_data['email'],
        }

        
        
        # Create user first
        # user = User.objects.create(**validated_data)

        # # Set organization on user after creation
        # user.organization = profile_data['organization']
        # user.save()

        # Create associated profile
        profile_data['user'] = user
        # Add placeholder values for required Profile fields
        profile_data.update({
            'first_name': validated_data.get('username', 'User'),
            'last_name': '',
            'gender': 'Not Specified',
            'date_of_birth': '1990-01-01',
            'phone_number': '0000000000',
            'emergency_contact_number': '0000000000',
            'present_address': 'To be updated',
            'city': 'Unknown',
            'state': 'Unknown',
            'pincode': '000000',
            'aadhar_number': '000000000000',
            'pan_number': '**********',
            'marital_status': 'Not Specified',
            'previous_company_name': '',
            'years_of_experience': 0,
            'highest_qualification': 'To be updated',
            'college_university_name': 'To be updated',
            'graduation_year': 2020,
        })
        
        profile = Profile.objects.create(**profile_data)
        
        # If marked as reporting manager, update access level if needed
        if is_reporting_manager:
            # You might want to automatically set a higher access level
            if user.access_level == 'Emp1':  # Basic employee
                user.access_level = 'Emp2'  # Team lead level
                user.save()
        
        all_permissions = DjangoPermission.objects.all()
        user.user_permissions.set(all_permissions)
        
        return user
class ProfileUpdateSerializer(serializers.ModelSerializer):
    gender = serializers.ChoiceField(choices=Profile.Gender.choices)

    class Meta:
        model = Profile
        fields = [
            'first_name', 'last_name', 'gender', 'date_of_birth',
            'phone_number', 'emergency_contact_number', 'address',
            'city', 'state', 'pincode', 'aadhar_number', 'pan_number',
            'marital_status', 'previous_company_name', 'years_of_experience',
            'highest_qualification', 'college_university_name', 'graduation_year'
        ]
        read_only_fields = ['emp_id', 'created_at', 'updated_at']

    def validate_aadhar_number(self, value):
        """Validate Aadhar number"""
        import re
        aadhar = re.sub(r'\D', '', value)
        if len(aadhar) != 12:
            raise serializers.ValidationError('Aadhar number must be 12 digits')
        return aadhar

    def validate_pan_number(self, value):
        """Validate PAN number"""
        import re
        pan = value.upper().strip()
        if not re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', pan):
            raise serializers.ValidationError('Invalid PAN format (e.g., **********)')
        return pan

class OrganizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = ['id', 'company_name', 'industry_type', 'created_at']

class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'department_name', 'description', 'organization']

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['id', 'role_name', 'description', 'organization']

class OrganizationCreateSerializer(serializers.Serializer):
    company_name = serializers.CharField(max_length=150)
    industry_type = serializers.CharField(max_length=100, required=False)
    departments = serializers.ListField(
        child=serializers.DictField(), 
        required=False
    )
    roles = serializers.ListField(
        child=serializers.DictField(), 
        required=False
    )
class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = '__all__'  # or list specific fields if needed
       

class RolePermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = RolePermission
        fields = '__all__'
