from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from .views import LogoutView, RegisterView, CustomTokenObtainPairView
from .views import *

urlpatterns = [
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('forgot-password/', ForgotPasswordView.as_view(), name='forgot_password'),
    path('reset-password/<str:token>/', ResetPasswordView.as_view(), name='reset_password'),
    path('change-password/', ChangePasswordView.as_view(), name='change_password'),
    path("roles/", RoleListCreateAPIView.as_view()),
    path("roles/<int:pk>/", RoleDetailAPIView.as_view()),
    path("permissions/", PermissionListCreateAPIView.as_view()),
    path("permissions/<int:pk>/", PermissionDetailAPIView.as_view()),
    path("role-permissions/", RolePermissionListCreateAPIView.as_view()),
    path("role-permissions/<int:pk>/", RolePermissionDetailAPIView.as_view()),
    path('organizations/<int:organization_id>/roles/', get_organization_roles, name='organization-roles'),
    path('organizations/<int:organization_id>/departments/', get_organization_departments, name='organization-departments'),
    path('departments/', DepartmentListCreate.as_view(), name='department-list-create'),
    path('departments/<int:pk>/', DepartmentDetail.as_view(), name='department-detail'),
]
