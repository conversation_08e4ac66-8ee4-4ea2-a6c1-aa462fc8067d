from django.db import models
from cryptography.fernet import <PERSON><PERSON><PERSON>
from django.conf import settings
import base64

class Encrypted<PERSON>ield(models.CharField):
    """
    A custom field that encrypts its contents before saving to database
    and decrypts when retrieving.
    """
    
    def __init__(self, *args, **kwargs):
        if not kwargs.get('max_length'):
            kwargs['max_length'] = 255  # Default max length for encrypted data
        super().__init__(*args, **kwargs)
        
        # Get or generate encryption key
        encryption_key = getattr(settings, 'ENCRYPTION_KEY', None)
        if not encryption_key:
            encryption_key = Fernet.generate_key()
        self.fernet = Fernet(encryption_key)

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        try:
            decoded = base64.b64decode(value.encode())
            decrypted = self.fernet.decrypt(decoded)
            return decrypted.decode()
        except Exception:
            return None

    def to_python(self, value):
        if isinstance(value, str) or value is None:
            return value
        return str(value)

    def get_prep_value(self, value):
        if value is None:
            return value
        encrypted = self.fernet.encrypt(str(value).encode())
        return base64.b64encode(encrypted).decode()
