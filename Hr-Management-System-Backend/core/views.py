import csv
from django.conf import settings
from django.core.exceptions import ValidationError as DjangoValidationError
from django.core.mail import send_mail
from django.db.models import Q, Count
from django.db import transaction  # ← ADD THIS LINE
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from itsdangerous import URLSafeTimedSerializer
from rest_framework import generics, permissions, status
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth import get_user_model
from .models import User, Organization, Department, Role, Permission, RolePermission
from .serializers import (
    RegisterSerializer,
    RoleSerializer,
    PermissionSerializer,
    RolePermissionSerializer,
    DepartmentSerializer,
)
from .validators import CustomPasswordValidator
from .throttles import LoginRateThrottle, PasswordResetThrottle, PasswordChangeThrottle, FailedLoginThrottle
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from profiles.serializers import ProfileSerializer
from profiles.models import Profile
from .permissions import DepartmentPermission
from .serializers import *

class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom login view with rate limiting
    """
    throttle_classes = [LoginRateThrottle]
    throttle_scope = 'login'
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            return response
        except (InvalidToken, TokenError) as e:
            # Apply stricter throttling for failed login attempts
            from .throttles import FailedLoginThrottle
            throttle = FailedLoginThrottle()
            if not throttle.allow_request(request, self):
                self.throttled(request, throttle.wait())
            raise e

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = RegisterSerializer
    permission_classes = [permissions.AllowAny]  # Adjust as needed
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """
        Enhanced registration with profile creation.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()
                profile = getattr(user, 'profile', None)
                response_data = {
                    'message': 'User registered successfully',
                    'user': {
                        'id': user.id,
                        'email': user.email,
                        'organization': getattr(user.organization, 'company_name', None),
                        'role': getattr(user.role, 'role_name', None),
                        'access_level': user.access_level,
                        #'profile': ProfileSerializer(profile).data if profile else None,
                    }
                }
                headers = self.get_success_headers(serializer.data)
                return Response(response_data, status=status.HTTP_201_CREATED, headers=headers)
            except Exception as e:
                return Response({'error': f'Registration failed: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"message": "Logged out successfully"}, status=status.HTTP_205_RESET_CONTENT)
        except Exception:
            return Response({"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)

def generate_reset_token(email):
    serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
    return serializer.dumps(email, salt="password-reset-salt")

def verify_reset_token(token, expiration=600):  # 10 minutes expiration
    serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
    try:
        email = serializer.loads(token, salt="password-reset-salt", max_age=expiration)
        return email
    except Exception:
        return None

class ForgotPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [PasswordResetThrottle]
    throttle_scope = 'password_reset'
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({"error": "Email is required"}, status=400)
        try:
            user = User.objects.get(email=email)
            token = generate_reset_token(user.email)
            reset_link = f"http://192.168.1.122:3000/reset-password/{token}/"
            
            # Send email (you must configure email backend in settings)
            send_mail(
                "Password Reset Request",
                f"Click the link to reset your password: {reset_link}",
                "<EMAIL>",
                [user.email],
                fail_silently=False,
            )
            return Response({"message": "Password reset link sent to your email."})
        except User.DoesNotExist:
            return Response({"error": "User not found."}, status=404)

class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [PasswordResetThrottle]
    throttle_scope = 'password_reset'
    def post(self, request, token):
        new_password = request.data.get('password')
        if not new_password:
            return Response({"error": "Password is required"}, status=400)

        # Validate token (5-minute expiry)
        email = verify_reset_token(token, expiration=300)
        if email is None:
            return Response({"error": "Invalid or expired token."}, status=400)

        # Validate password using your custom validator
        validator = CustomPasswordValidator()
        try:
            validator.validate(new_password)
        except DjangoValidationError as e:
            raise DRFValidationError({"password": e.messages})

        # Reset the password
        try:
            user = User.objects.get(email=email)
            user.set_password(new_password)
            user.save()
            return Response({"message": "Password reset successful."})
        except User.DoesNotExist:
            return Response({"error": "User not found."}, status=404)
        
class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]
    throttle_classes = [PasswordChangeThrottle]
    throttle_scope = 'password_change'
    def post(self, request):
        user = request.user

        current_password = request.data.get("current_password")
        new_password = request.data.get("new_password")
        confirm_password = request.data.get("confirm_password")

        # Check all fields are provided
        if not all([current_password, new_password, confirm_password]):
            return Response({"error": "All fields are required."}, status=status.HTTP_400_BAD_REQUEST)

        # Check current password is correct
        if not user.check_password(current_password):
            return Response({"error": "Current password is incorrect."}, status=status.HTTP_400_BAD_REQUEST)

        # Check new and confirm password match
        if new_password != confirm_password:
            return Response({"error": "New password and confirm password do not match."}, status=status.HTTP_400_BAD_REQUEST)


        # Set new password
        user.set_password(new_password)
        user.save()

        return Response({"success": "Password changed successfully."}, status=status.HTTP_200_OK)

class RoleListCreateAPIView(APIView):
    def get(self, request):
        roles = Role.objects.all()
        serializer = RoleSerializer(roles, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = RoleSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else :
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RoleDetailAPIView(APIView):
    def get(self, request, pk):
        role = get_object_or_404(Role, pk=pk)
        serializer = RoleSerializer(role)
        return Response(serializer.data)

    def put(self, request, pk):
        role = get_object_or_404(Role, pk=pk)
        serializer = RoleSerializer(role, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        role = get_object_or_404(Role, pk=pk)
        role.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

 
class PermissionListCreateAPIView(APIView):
    def get(self, request):
        permissions = Permission.objects.all()
        serializer = PermissionSerializer(permissions, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = PermissionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PermissionDetailAPIView(APIView):
    def get(self, request, pk):
        permission = get_object_or_404(Permission, pk=pk)
        serializer = PermissionSerializer(permission)
        return Response(serializer.data)

    def put(self, request, pk):
        permission = get_object_or_404(Permission, pk=pk)
        serializer = PermissionSerializer(permission, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        permission = get_object_or_404(Permission, pk=pk)
        permission.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
class RolePermissionListCreateAPIView(APIView):
    def get(self, request):
        role_permissions = RolePermission.objects.all()
        serializer = RolePermissionSerializer(role_permissions, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = RolePermissionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RolePermissionDetailAPIView(APIView):
    def get(self, request, pk):
        role_permission = get_object_or_404(RolePermission, pk=pk)
        serializer = RolePermissionSerializer(role_permission)
        return Response(serializer.data)

    def put(self, request, pk):
        role_permission = get_object_or_404(RolePermission, pk=pk)
        serializer = RolePermissionSerializer(role_permission, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        role_permission = get_object_or_404(RolePermission, pk=pk)
        role_permission.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_organization_roles(request, organization_id):
    """Get roles for a specific organization"""
    try:
        roles = Role.objects.filter(organization_id=organization_id)
        role_data = [{'id': role.id, 'role_name': role.role_name} for role in roles]
        return Response(role_data)
    except Exception as e:
        return Response({'error': str(e)}, status=400)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_organization_departments(request, organization_id):
    """Get departments for a specific organization"""
    try:
        departments = Department.objects.filter(organization_id=organization_id)
        dept_data = [{'id': dept.id, 'department_name': dept.department_name} for dept in departments]
        return Response(dept_data)
    except Exception as e:
        return Response({'error': str(e)}, status=400)

class AvailableReportingManagersView(generics.ListAPIView):
    """Get available reporting managers for an organization"""
    serializer_class = ProfileSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        organization_id = self.request.query_params.get('organization')
        if organization_id:
            return Profile.objects.filter(
                organization_id=organization_id,
                user__access_level__in=['Emp2', 'Emp3', 'Admin']  # Only managers and above
            ).select_related('user')
        return Profile.objects.none()


class DepartmentListCreate(APIView):
    permission_classes = [DepartmentPermission]

    def get(self, request):
        departments = Department.objects.all()
        serializer = DepartmentSerializer(departments, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = DepartmentSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DepartmentDetail(APIView):
    permission_classes = [DepartmentPermission]

    def get_object(self, pk):
        return get_object_or_404(Department, pk=pk)

    def get(self, request, pk):
        department = self.get_object(pk)
        serializer = DepartmentSerializer(department)
        return Response(serializer.data)

    def put(self, request, pk):
        department = self.get_object(pk)
        serializer = DepartmentSerializer(department, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        department = self.get_object(pk)
        department.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
