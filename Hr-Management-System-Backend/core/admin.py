from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django import forms
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import User, Organization, Role, Permission, RolePermission, Department

# Import Profile model
try:
    from profiles.models import Profile
except ImportError:
    Profile = None

# Custom forms for User admin
class CustomUserCreationForm(UserCreationForm):
    """Custom user creation form for admin"""
    class Meta(UserCreationForm.Meta):
        model = User
        fields = ("email", "organization", "role")

    if Profile:
        reporting_manager = forms.ModelChoiceField(
            queryset=Profile.objects.all(),
            required=False,
            empty_label="No manager",
            help_text="Select reporting manager"
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['organization'].required = True
        self.fields['role'].required = True
        
        # Filter roles based on selected organization
        if 'organization' in self.data:
            try:
                organization_id = int(self.data.get('organization'))
                self.fields['role'].queryset = Role.objects.filter(organization_id=organization_id)
            except (ValueError, TypeError):
                self.fields['role'].queryset = Role.objects.none()
        else:
            self.fields['role'].queryset = Role.objects.none()

class CustomUserChangeForm(UserChangeForm):
    """Custom user change form for admin"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add reporting manager field only if Profile model is available
        if Profile:
            self.fields['reporting_manager'] = forms.ModelChoiceField(
                queryset=Profile.objects.all(),
                required=False,
                empty_label="No manager",
                help_text="Select reporting manager"
            )
            
            # Filter roles based on user's organization
            if self.instance and self.instance.organization:
                self.fields['role'].queryset = Role.objects.filter(organization=self.instance.organization)
                # Filter reporting managers to same organization
                self.fields['reporting_manager'].queryset = Profile.objects.filter(
                    organization=self.instance.organization
                ).exclude(user=self.instance)
            
            # Set initial value for reporting manager
            if self.instance and hasattr(self.instance, 'profile') and self.instance.profile:
                self.fields['reporting_manager'].initial = self.instance.profile.reporting_manager
        
    class Meta(UserChangeForm.Meta):
        model = User
        fields = '__all__'

    def save(self, commit=True):
        user = super().save(commit)
        if commit and Profile and hasattr(user, 'profile') and user.profile:
            reporting_manager = self.cleaned_data.get('reporting_manager')
            if reporting_manager:
                user.profile.reporting_manager = reporting_manager
                user.profile.save()
        return user

# Inline classes
class RolePermissionInline(admin.TabularInline):
    """Inline for managing role permissions"""
    model = RolePermission
    extra = 0
    autocomplete_fields = ['permission']

class RoleInline(admin.TabularInline):
    """Inline for managing organization roles"""
    model = Role
    extra = 0
    fields = ['role_name', 'description']
    readonly_fields = ['created_at']

class DepartmentInline(admin.TabularInline):
    """Inline for managing organization departments"""
    model = Department
    extra = 0
    fields = ['department_name', 'description']
    readonly_fields = ['created_at']

# Admin classes
@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """Admin interface for Organization model"""
    list_display = ['company_name', 'industry_type', 'total_users', 'total_roles', 'total_departments', 'created_at']
    list_filter = ['industry_type', 'created_at']
    search_fields = ['company_name', 'industry_type']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [RoleInline, DepartmentInline]
    
    fieldsets = (
        ('Organization Information', {
            'fields': ('company_name', 'industry_type')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_users(self, obj):
        """Count total users in organization"""
        count = obj.user_set.count()
        if count > 0:
            url = reverse('admin:core_user_changelist') + f'?organization__id__exact={obj.id}'
            return format_html('<a href="{}">{} users</a>', url, count)
        return count
    total_users.short_description = 'Total Users'
    
    def total_roles(self, obj):
        """Count total roles in organization"""
        count = obj.role_set.count()
        return count
    total_roles.short_description = 'Roles'
    
    def total_departments(self, obj):
        """Count total departments in organization"""
        count = obj.department_set.count()
        return count
    total_departments.short_description = 'Departments'

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """Admin interface for Role model"""
    list_display = ['role_name', 'organization', 'total_users', 'total_permissions', 'created_at']
    list_filter = ['organization', 'created_at']
    search_fields = ['role_name', 'description', 'organization__company_name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [RolePermissionInline]
    autocomplete_fields = ['organization']
    
    fieldsets = (
        ('Role Information', {
            'fields': ('organization', 'role_name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_users(self, obj):
        """Count users with this role"""
        count = obj.user_set.count()
        if count > 0:
            url = reverse('admin:core_user_changelist') + f'?role__id__exact={obj.id}'
            return format_html('<a href="{}">{} users</a>', url, count)
        return count
    total_users.short_description = 'Users'
    
    def total_permissions(self, obj):
        """Count permissions for this role"""
        count = obj.rolepermission_set.count()
        return count
    total_permissions.short_description = 'Permissions'

@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """Admin interface for Permission model"""
    list_display = ['permission_name', 'description', 'total_roles', 'created_at']
    list_filter = ['created_at']
    search_fields = ['permission_name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Permission Information', {
            'fields': ('permission_name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_roles(self, obj):
        """Count roles with this permission"""
        count = obj.rolepermission_set.count()
        return count
    total_roles.short_description = 'Roles'

@admin.register(RolePermission)
class RolePermissionAdmin(admin.ModelAdmin):
    """Admin interface for RolePermission model"""
    list_display = ['role', 'permission', 'role_organization', 'created_at'
]
    list_filter = ['role__organization', 'created_at']
    search_fields = ['role__role_name', 'permission__permission_name', 'role__organization__company_name']
    autocomplete_fields = ['role', 'permission']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Role Permission Mapping', {
            'fields': ('role', 'permission')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def role_organization(self, obj):
        """Show organization of the role"""
        return obj.role.organization.company_name
    role_organization.short_description = 'Organization'

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """Admin interface for Department model"""
    list_display = ['department_name', 'organization', 'total_employees', 'created_at']
    list_filter = ['organization', 'created_at']
    search_fields = ['department_name', 'description', 'organization__company_name']
    readonly_fields = ['created_at', 'updated_at']
    autocomplete_fields = ['organization']
    
    fieldsets = (
        ('Department Information', {
            'fields': ('organization', 'department_name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_employees(self, obj):
        """Count employees in department"""
        try:
            from profiles.models import Profile
            count = Profile.objects.filter(department_ref=obj).count()
            if count > 0:
                url = reverse('admin:profiles_profile_changelist') + f'?department_ref__id__exact={obj.id}'
                return format_html('<a href="{}">{} employees</a>', url, count)
            return count
        except ImportError:
            return 'N/A'
    total_employees.short_description = 'Employees'

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Custom admin interface for User model"""
    form = CustomUserChangeForm
    add_form = CustomUserCreationForm
    
    list_display = [
        'email', 'get_full_name', 'organization', 'role', 
        'access_level', 'get_reporting_manager', 'is_active', 'is_staff', 'last_login', 'created_at'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'access_level', 
        'organization', 'role', 'created_at'
    ]
    search_fields = ['email', 'organization__company_name', 'role__role_name']
    readonly_fields = ['last_login', 'created_at', 'updated_at', 'get_profile_link']
    autocomplete_fields = ['organization', 'role']
    filter_horizontal = ()
    
    def get_form(self, request, obj=None, **kwargs):
        """Override to handle custom reporting_manager field"""
        # Get the fieldsets to determine which fields to include
        fieldsets = self.get_fieldsets(request, obj)
        fields = []
        for fieldset in fieldsets:
            fields.extend(fieldset[1]['fields'])

        # Remove reporting_manager from fields if Profile is not available
        if not Profile and 'reporting_manager' in fields:
            fields = [f for f in fields if f != 'reporting_manager']

        # Remove reporting_manager from readonly_fields if not Profile
        readonly_fields = list(self.readonly_fields)
        if not Profile and 'reporting_manager' in readonly_fields:
            readonly_fields.remove('reporting_manager')

        # Only include fields that are actually on the User model or added by the form
        valid_fields = [f for f in fields if hasattr(self.model, f) or f in getattr(self.form, 'base_fields', {}) or f in getattr(self, 'get_form_additional_fields', lambda: [])()]

        kwargs['fields'] = [f for f in valid_fields if f not in readonly_fields]
        return super().get_form(request, obj, **kwargs)
    
    def get_fieldsets(self, request, obj=None):
        """Dynamic fieldsets based on Profile availability"""
        base_fieldsets = [
            ('Authentication', {
                'fields': ('email', 'password')
            }),
            ('Organization & Role', {
                'fields': ('organization', 'role', 'access_level')
            }),
        ]
        # Add reporting manager section only if Profile is available
        if Profile:
            base_fieldsets.append(
                ('Reporting Structure', {
                    'fields': ('reporting_manager',),
                })
            )
        base_fieldsets.extend([
            ('Profile Information', {
                'fields': ('get_profile_link',),
                'classes': ('collapse',)
            }),
            ('Permissions', {
                'fields': ('is_active', 'is_staff', 'is_superuser'),
            }),
            ('Important Dates', {
                'fields': ('last_login', 'created_at', 'updated_at'),
                'classes': ('collapse',)
            }),
        ])
        return base_fieldsets
    
    add_fieldsets = (
        ('Create New User', {
            'classes': ('wide',),
            'fields': ('email', 'organization', 'role', 'password1', 'password2', 'reporting_manager') if Profile else ('email', 'organization', 'role', 'password1', 'password2')
        }),
        ('Additional Information', {
            'classes': ('wide',),
            'fields': ('access_level', 'is_active', 'is_staff')
        }),
    )
    
    def get_reporting_manager(self, obj):
        """Display reporting manager from profile"""
        try:
            if hasattr(obj, 'profile') and obj.profile and obj.profile.reporting_manager:
                manager = obj.profile.reporting_manager
                return format_html('<a href="/admin/profiles/profile/{}/change/">{}</a>', 
                                 manager.id, f"{manager.first_name} {manager.last_name}")
            return "No manager assigned"
        except:
            return "No profile"
    get_reporting_manager.short_description = 'Reporting Manager'
    
    def get_profile_link(self, obj):
        """Link to user's profile"""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                url = reverse('admin:profiles_profile_change', args=[obj.profile.id])
                return format_html('<a href="{}">Edit Profile & Reporting Manager</a>', url)
            else:
                url = reverse('admin:profiles_profile_add') + f'?user={obj.id}'
                return format_html('<a href="{}">Create Profile</a>', url)
        except:
            return "Profile not available"
    get_profile_link.short_description = 'Profile Management'
    
    ordering = ['-created_at']
    
    def get_full_name(self, obj):
        """Display full name from profile if available"""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                full_name = obj.get_full_name()
                if full_name != obj.email:
                    return format_html('<span title="Profile: {}">{}</span>', obj.profile.emp_id, full_name)
            return obj.email
        except:
            return obj.email
    get_full_name.short_description = 'Full Name'
    
    def get_queryset(self, request):
        """Optimize queries with select_related"""
        return super().get_queryset(request).select_related(
            'organization', 'role'
        ).prefetch_related('role__rolepermission_set')
    
    def save_model(self, request, obj, form, change):
        """Custom save logic for user model"""
        is_new = not change

        # Hash password if new user
        if is_new and hasattr(form, 'cleaned_data') and 'password1' in form.cleaned_data:
            obj.set_password(form.cleaned_data['password1'])

        super().save_model(request, obj, form, change)

        # If it's a new user, assign all permissions
        if is_new:
            all_permissions = Permission.objects.all()
            obj.user_permissions.set(all_permissions)
            obj.save()
# Custom admin site configuration
admin.site.site_header = 'Mhcognition HR Management System'
admin.site.site_title = 'MHCognition HRMS Admin'
admin.site.index_title = 'Welcome to MHCognition HR Management System Administration'

