# Core Django Requirements
Django>=5.0,<6.0
djangorestframework>=3.14.0
djangorestframework-simplejwt>=5.3.0
django-cors-headers>=4.3.1

# Database and Security
psycopg2-binary>=2.9.7
cryptography>=41.0.2
itsdangerous>=2.1.2

# Environment and Configuration
python-decouple>=3.8
python-dotenv>=1.0.0

# File Handling and Data Processing
Pillow>=10.1.0  # For image processing
pandas>=2.1.0   # For Excel/CSV handling
openpyxl>=3.1.2 # For Excel file support
xlrd>=2.0.1     # For older Excel formats
python-magic>=0.4.27  # For file type detection

# API Documentation
drf-yasg>=1.21.7

# Caching and Performance
redis>=5.0.0
django-redis>=5.4.0
whitenoise>=6.6.0

# Date/Time Handling
python-dateutil>=2.8.2
pytz>=2023.3

# Password Management
django-password-validators>=1.7.1

# Rate Limiting
django-ratelimit>=4.1.0

# Email Handling
django-anymail>=10.2

# Development Tools
django-extensions>=3.2.3
django-debug-toolbar>=4.2.0

# Testing
pytest-django>=4.7.0
factory-boy>=3.3.0
coverage>=7.3.2

# Production Server
gunicorn>=21.2.0

# Error Tracking
sentry-sdk>=1.38.0