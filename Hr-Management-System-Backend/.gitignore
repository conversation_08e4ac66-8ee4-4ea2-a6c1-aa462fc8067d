# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.marscode
# C extensions
*.so
hrm_env/
# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development
.env.staging
.env.production
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Django specific
*.pyc
*.pyo
*.pyd
migrations/
staticfiles/
static/
media/
uploads/

# Database
*.sqlite3
*.db

# Logs
*.log
logs/
django.log

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (for frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Coverage reports
htmlcov/
.coverage
coverage.xml
*.cover

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp

# Email backend files
sent_emails/

# Redis dump file
dump.rdb

# Celery beat schedule files
celerybeat-schedule.*
celerybeat.pid

# Configuration files with sensitive data
config.ini
config.json
secrets.json
secrets.yaml
.secret

# JWT and authentication related
token_blacklist/
session_data/

# Custom settings files
settings_local.py
local_settings.py
dev_settings.py
prod_settings.py

# Cache directories
.cache/
cache/
django_cache/

# Testing
.pytest_cache/
test_results/

# Documentation
docs/_build/
site/

# Webpack
webpack-stats.json
webpack-bundle-tracker/

# Compressed files
*.zip
*.tar.gz
*.rar

# Local development
.vagrant/
Vagrantfile.local

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.yaml.local
*.yml.local

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Application specific
password_reset_tokens/
user_uploads/
export_files/
