from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    ProfileViewSet,
    BankDetailsViewSet,
    SubmitProfileUpdateView,
    ProfileUpdateApprovalViewSet,
    Hr3OnboardingViewSet,
)

router = DefaultRouter()
router.register(r'profiles', ProfileViewSet, basename='profiles')
router.register(r'bank-details', BankDetailsViewSet, basename='bank-details')
router.register(r'profile-update-requests', ProfileUpdateApprovalViewSet, basename='profile-update-requests')
router.register(r'hr3-onboarding', Hr3OnboardingViewSet, basename='hr3-onboarding')

urlpatterns = [
    # Separate endpoint for employees to submit update requests
    path('profiles/submit-update/', SubmitProfileUpdateView.as_view(), name='submit-profile-update'),
    path('api/', include(router.urls)),
]

# Include router-generated endpoints (profiles, bank-details, profile-update-requests)
urlpatterns += router.urls
