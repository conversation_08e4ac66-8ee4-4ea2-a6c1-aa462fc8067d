from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.db import transaction
from .models import Profile, BankDetails, ProfileUpdateRequest, OnboardingSession
from .serializers import (
    ProfileSerializer, 
    BankDetailsSerializer, 
    ProfileUpdateRequestSerializer,
    OnboardingSessionSerializer,
    OnboardingStepOneSerializer,
    OnboardingStepThreeSerializer
)
from core.models import User
from core.decorators import role_required
from .permissions import (
    ProfilePermission, 
    IsHR3, 
    IsOwnerProfileSubmitter,
    IsHR3OnboardingPermission
)
from django.core.files.base import ContentFile
from rest_framework.parsers import MultiPartParser, FormParser
import pandas as pd
import io, csv
from django.contrib.auth.hashers import make_password
from django.core.mail import send_mail
import secrets
import string
from .models import *
from .serializers import *
from .permissions import *

class Hr3OnboardingViewSet(viewsets.ModelViewSet):
    queryset = OnboardingSession.objects.all()
    serializer_class = OnboardingSessionSerializer
    permission_classes = [IsAuthenticated, IsHR3OnboardingPermission]
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        return OnboardingSession.objects.filter(created_by=self.request.user)

    @action(detail=False, methods=['post'], url_path='step-one')
    def step_one_basic_info(self, request):
        """Step 1: Basic employee information and auto-generation"""
        serializer = OnboardingStepOneSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            
            # Generate emp_id
            last_profile = Profile.objects.order_by('-emp_id').first()
            if last_profile:
                last_num = int(last_profile.emp_id.replace('EMP', ''))
                emp_id = f'EMP{last_num + 1:03d}'
            else:
                emp_id = 'EMP001'
            
            # Generate company email
            org = data['organization']
            company_email = f"{data['first_name'].lower()}.{data['last_name'].lower()}@{org.email_domain}"
            
            # Create onboarding session
            session = OnboardingSession.objects.create(
                created_by=request.user,
                organization=org,
                employee_data={
                    'first_name': data['first_name'],
                    'last_name': data['last_name'],
                    'personal_email': data['personal_email'],
                    'generated_emp_id': emp_id,
                    'company_email': company_email
                }
            )
            
            return Response({
                'session_id': session.id,
                'generated_emp_id': emp_id,
                'company_email': company_email
            })
        return Response(serializer.errors, status=400)

    @action(detail=True, methods=['post'], url_path='step-two-upload')
    def step_two_upload_validate(self, request, pk=None):
        """Step 2: File upload and validation"""
        session = self.get_object()
        
        if 'file' not in request.FILES:
            return Response({'error': 'No file uploaded'}, status=400)
        
        file = request.FILES['file']
        session.uploaded_file = file
        session.status = 'validating'
        session.save()
        
        # Validate file
        validation_errors = self._validate_employee_file(file)
        
        if validation_errors:
            session.status = 'failed'
            session.validation_errors = validation_errors
            session.save()
            return Response({
                'status': 'validation_failed',
                'errors': validation_errors
            }, status=400)
        
        session.status = 'validated'
        session.save()
        
        return Response({
            'status': 'validated',
            'preview_data': self._extract_preview_data(file)
        })

    @action(detail=True, methods=['post'], url_path='step-three-assign')
    def step_three_role_assignment(self, request, pk=None):
        """Step 3: Role and access assignment"""
        session = self.get_object()
        serializer = OnboardingStepThreeSerializer(data=request.data)
        
        if serializer.is_valid():
            # Store assignment data in session
            session.employee_data.update(serializer.validated_data)
            session.save()
            
            return Response({'status': 'assignments_saved'})
        return Response(serializer.errors, status=400)

    @action(detail=True, methods=['post'], url_path='step-four-create')
    def step_four_create_employee(self, request, pk=None):
        """Step 4: Create employee and send notification"""
        session = self.get_object()
        
        if session.status != 'validated':
            return Response({'error': 'Session not validated'}, status=400)
        
        try:
            with transaction.atomic():
                # Generate temporary password
                temp_password = self._generate_temp_password()
                
                # Create User
                user = User.objects.create(
                    email=session.employee_data['company_email'],
                    organization=session.organization,
                    role_id=session.employee_data['role'],
                    access_level=session.employee_data['access_level'],
                    password=make_password(temp_password)
                )
                
                # Create Profile with file data
                profile_data = self._extract_profile_data(session.uploaded_file)
                profile_data.update({
                    'emp_id': session.employee_data['generated_emp_id'],
                    'user': user,
                    'organization': session.organization,
                    'first_name': session.employee_data['first_name'],
                    'last_name': session.employee_data['last_name'],
                    'email_id': session.employee_data['company_email'],
                    'department_ref_id': session.employee_data['department'],
                    'reporting_manager_id': session.employee_data.get('reporting_manager'),
                    'date_of_joining': session.employee_data['date_of_joining']
                })
                
                profile = Profile.objects.create(**profile_data)
                
                # Send notification email
                self._send_welcome_email(
                    session.employee_data['personal_email'],
                    session.employee_data['first_name'],
                    session.employee_data['company_email'],
                    temp_password
                )
                
                session.status = 'completed'
                session.save()
                
                return Response({
                    'status': 'employee_created',
                    'emp_id': profile.emp_id,
                    'user_id': user.id
                })
                
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    def _validate_employee_file(self, file):
        """Validate uploaded employee file"""
        errors = {}
        try:
            if file.name.endswith('.xlsx'):
                df = pd.read_excel(file)
            else:
                return {'file': 'Only Excel files are supported'}
            
            # Validate required fields
            required_fields = ['phone_number', 'aadhar_number', 'pan_number', 'date_of_birth']
            for field in required_fields:
                if field not in df.columns:
                    errors[field] = f'Required field {field} missing'
            
            # Validate data formats
            for index, row in df.iterrows():
                if 'phone_number' in df.columns:
                    phone = str(row['phone_number'])
                    if not phone.isdigit() or len(phone) != 10:
                        errors[f'row_{index}_phone'] = 'Invalid phone number format'
                
                if 'aadhar_number' in df.columns:
                    aadhar = str(row['aadhar_number'])
                    if not aadhar.isdigit() or len(aadhar) != 12:
                        errors[f'row_{index}_aadhar'] = 'Invalid Aadhaar format'
            
            # Check duplicates
            if 'email_id' in df.columns:
                for email in df['email_id']:
                    if Profile.objects.filter(email_id=email).exists():
                        errors[f'duplicate_email_{email}'] = 'Email already exists'
            
        except Exception as e:
            errors['file_processing'] = str(e)
        
        return errors

    def _generate_temp_password(self):
        """Generate secure temporary password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%"
        return ''.join(secrets.choice(alphabet) for _ in range(12))

    def _send_welcome_email(self, personal_email, first_name, company_email, temp_password):
        """Send welcome email to new employee"""
        subject = 'Welcome to the Company - Login Credentials'
        message = f"""
        Dear {first_name},
        
        Welcome to our organization! Your account has been created.
        
        Login Details:
        Email: {company_email}
        Temporary Password: {temp_password}
        
        Please login and change your password immediately.
        
        Best regards,
        HR Team
        """
        
        send_mail(subject, message, '<EMAIL>', [personal_email])

class ProfileViewSet(viewsets.ModelViewSet):
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated, ProfilePermission]

    def get_queryset(self):
        user = self.request.user

        if getattr(self, 'swagger_fake_view', False):
            return Profile.objects.none()

        if not user.is_authenticated:
            return Profile.objects.none()

        # HR3 has full access
        if getattr(user, 'access_level', None) == 'Hr3':
            return Profile.objects.all()

        # Emp1 can only see their own profile
        if getattr(user, 'access_level', None) == 'Emp1' and hasattr(user, 'profile') and user.profile:
            return Profile.objects.filter(emp_id=user.profile.emp_id)

        # Emp2 can see direct reports
        elif getattr(user, 'access_level', None) == 'Emp2' and hasattr(user, 'profile') and user.profile:
            return Profile.objects.filter(reporting_manager=user.profile)

        # Emp3 can see by department
        elif getattr(user, 'access_level', None) == 'Emp3' and hasattr(user, 'profile') and user.profile:
            return Profile.objects.filter(department_ref=user.profile.department_ref)

        return Profile.objects.none()

    @action(detail=False, methods=['get'], url_path='me')
    def me(self, request):
        """Fetch the profile of the currently logged-in user."""
        if not hasattr(request.user, 'profile') or request.user.profile is None:
            return Response({"error": "No profile associated with user."}, status=404)

        serializer = self.get_serializer(request.user.profile)
        return Response(serializer.data)

    # ----------------------------
    # STEP 1: Validate Employee
    # ----------------------------
    @action(detail=False, methods=['post'], url_path='validate')
    def validate_employee(self, request):
        emp_id = request.data.get("emp_id")
        email = request.data.get("email_id")

        if not emp_id or not email:
            return Response({"valid": False, "message": "emp_id and email_id are required"}, status=400)

        exists = Profile.objects.filter(emp_id=emp_id, email_id=email).exists()
        if exists:
            return Response({"valid": True, "message": "Employee exists in system"})
        else:
            return Response({"valid": False, "message": "Employee not found. Please check ID and Email"}, status=404)

    # ----------------------------
    # STEP 2: Upload Excel
    # ----------------------------
    @action(detail=False, methods=['post'], url_path='upload-excel', parser_classes=[MultiPartParser, FormParser])
    def upload_excel(self, request):
        file = request.FILES.get("file")
        if not file:
            return Response({"error": "No file uploaded"}, status=400)

        try:
            # Handle both CSV and Excel
            if file.name.endswith(".csv"):
                df = pd.read_csv(file)
            else:
                df = pd.read_excel(file)

            preview = df.head(5).to_dict(orient="records")
            request.session["uploaded_data"] = df.to_dict(orient="records")  # temp storage in session

            return Response({
                "message": "File uploaded successfully",
                "preview": preview,
                "columns": list(df.columns)
            })
        except Exception as e:
            return Response({"error": f"Failed to parse file: {str(e)}"}, status=400)

    # ----------------------------
    # STEP 3: Bulk Create Profiles
    # ----------------------------
    @action(detail=False, methods=['post'], url_path='bulk-create')
    def bulk_create_profiles(self, request):
        data = request.session.get("uploaded_data")
        if not data:
            return Response({"error": "No uploaded data found. Please upload Excel first."}, status=400)

        success_count = 0
        failure_count = 0
        error_log = []

        with transaction.atomic():
            for row in data:
                try:
                    serializer = ProfileSerializer(data=row)
                    serializer.is_valid(raise_exception=True)
                    serializer.save()
                    success_count += 1
                except Exception as e:
                    failure_count += 1
                    error_log.append({
                        "row": row,
                        "error": str(e)
                    })

        # Export error log if failures exist
        error_file = None
        if error_log:
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=["row", "error"])
            writer.writeheader()
            for err in error_log:
                writer.writerow(err)
            error_file = ContentFile(output.getvalue().encode("utf-8"), name="error_log.csv")

        return Response({
            "message": "Bulk profile creation complete",
            "success": success_count,
            "failed": failure_count,
            "error_log": error_file.name if error_file else None
        })


class BankDetailsViewSet(viewsets.ModelViewSet):
    queryset = BankDetails.objects.all()
    serializer_class = BankDetailsSerializer
    permission_classes = [IsAuthenticated, ProfilePermission]

    def get_queryset(self):
        user = self.request.user

        if getattr(self, 'swagger_fake_view', False):
            return BankDetails.objects.none()

        if not user.is_authenticated:
            return BankDetails.objects.none()

        # HR3 has full access
        if getattr(user, 'access_level', None) == 'Hr3':
            return BankDetails.objects.all()

        if hasattr(user, 'profile') and user.profile:
            return BankDetails.objects.filter(profile=user.profile)

        return BankDetails.objects.none()


class SubmitProfileUpdateView(APIView):
    """
    Allows employees to submit a profile update request
    which will be reviewed by HR3.
    """
    permission_classes = [IsAuthenticated, IsOwnerProfileSubmitter]

    def post(self, request):
        user = request.user
        if not hasattr(user, 'profile') or user.profile is None:
            return Response({"error": "No profile linked."}, status=400)

        if ProfileUpdateRequest.objects.filter(profile=user.profile, status='Pending').exists():
            return Response({"error": "You already have a pending update request."}, status=400)

        serializer = ProfileSerializer(instance=user.profile, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        ProfileUpdateRequest.objects.create(
            profile=user.profile,
            data=serializer.validated_data,
            status='Pending'
        )
        return Response({"message": "Update request submitted for HR review."})


class ProfileUpdateApprovalViewSet(viewsets.ModelViewSet):
    """
    HR3 can approve or reject submitted profile update requests.
    """
    queryset = ProfileUpdateRequest.objects.all()
    serializer_class = ProfileUpdateRequestSerializer
    permission_classes = [IsAuthenticated, IsHR3]

    @action(detail=True, methods=['post'], url_path='approve')
    def approve(self, request, pk=None):
        req = self.get_object()
        if req.status != 'Pending':
            return Response({"error": "Already reviewed."}, status=400)

        for field, value in req.data.items():
            setattr(req.profile, field, value)
        req.profile.save()

        req.status = 'Approved'
        req.reviewed_by = request.user
        req.reviewed_at = datetime.now()
        req.save()

        return Response({
            "message": f"Profile update for {req.profile.emp_id} approved and applied."
        })

    @action(detail=True, methods=['post'], url_path='reject')
    def reject(self, request, pk=None):
        req = self.get_object()
        if req.status != 'Pending':
            return Response({"error": "Already reviewed."}, status=400)

        req.status = 'Rejected'
        req.reviewed_by = request.user
        req.reviewed_at = datetime.now()
        req.comments = request.data.get('comments', '')
        req.save()

        return Response({"message": "Profile update rejected."})
