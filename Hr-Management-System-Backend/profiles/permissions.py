from rest_framework.permissions import BasePermission, SAFE_METHODS


class ProfilePermission(BasePermission):
    """
    Universal permission for Profile and related models:
    - Employees (Emp1–Emp4) → can view/edit their own profile & related objects.
    - HR1 → can view all, but not modify.
    - HR2, HR3 → can view + modify all.
    """

    def has_object_permission(self, request, view, obj):
        user = request.user

        if not user or not user.is_authenticated:
            return False

        access_level = getattr(user, "access_level", None)

        # Figure out which Profile this object belongs to
        if hasattr(obj, "user"):  
            # Directly a Profile
            profile_user = obj.user
        elif hasattr(obj, "profile"):
            # Related object (BankDetails, FamilyMember, etc.)
            profile_user = obj.profile.user
        else:
            return False  # Not linked to Profile

        # --- SAFE METHODS (GET, HEAD, OPTIONS) ---
        if request.method in SAFE_METHODS:
            if access_level and access_level.startswith("Emp"):
                return profile_user == user  # Employee can only view their own
            if access_level in ["Hr1", "Hr2", "Hr3"]:
                return True  # HR can view all
            return False

        # --- WRITE METHODS (POST, PUT, PATCH, DELETE) ---
        if request.method in ["POST", "PUT", "PATCH", "DELETE"]:
            if access_level in ["Hr2", "Hr3"]:
                return True  # HR2 & HR3 can edit all
            if access_level and access_level.startswith("Emp"):
                return profile_user == user  # Employees can edit their own
            return False

        return False


class IsHR3(BasePermission):
    """
    Allows access only to users with access_level = Hr3.
    Use this for HR Director–level actions (e.g., approvals).
    """
    def has_permission(self, request, view):
        return (
            request.user
            and request.user.is_authenticated
            and getattr(request.user, "access_level", None) == "Hr3"
        )


class IsHR3OnboardingPermission(BasePermission):
    """
    Only Hr3 users can access employee onboarding flow
    """
    def has_permission(self, request, view):
        return (
            request.user
            and request.user.is_authenticated
            and getattr(request.user, "access_level", None) == "Hr3"
        )


class IsOwnerProfileSubmitter(BasePermission):
    """
    Employees can submit update requests for their own profile only.
    """
    def has_permission(self, request, view):
        user = request.user
        return user and user.is_authenticated and hasattr(user, "profile")

    def has_object_permission(self, request, view, obj):
        user = request.user
        return obj.profile.user == user
