import sys
sys.path.append("/home/<USER>/Coding stuff/Hr-Management-System-Backend")

import os
import django
from datetime import datetime
import openpyxl

# ---------------- Django Setup ----------------
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "base.settings")
django.setup()

from profiles.models import Profile

# ---------------- Open Excel ----------------
excel_path = r"/home/<USER>/Downloads/updatedOBform(2).xlsx"
wb = openpyxl.load_workbook(excel_path, data_only=True)
sheet = wb.active  # Uses the first sheet

# ---------------- Field to cell mapping ----------------
field_to_cell = {
    "first_name": "D5",
    "middle_name": "D6",
    "last_name": "D7",
    "date_of_birth": "I5",
    "gender": "L5",
    "birth_place": "I6",
    "age": "L6",
    "marriage_date": "I7",
    "marital_status": "L7",
    "nationality": "D8",
    "religion": "H8",
    "native_state": "L8",
    "state_of_domicile": "L9",
    "phone_number": "D9",
    "emergency_contact_number": "H9",
    "email_id": "C10",
    "present_address": ["D11", "B12"],
    "permanent_address": ["L11", "I12"],
    "present_address_pin_code": "D13",
    "permanent_address_pin_code": "L13",
    "pan_number": "K19",
    "aadhar_number": "K20"
}

def get_cell_value(cell):
    return sheet[cell].value

# ---------------- Extract and Process Data ----------------
extracted_data = {}

for field, cells in field_to_cell.items():
    if isinstance(cells, list):
        value = " ".join([str(get_cell_value(cell) or "") for cell in cells]).strip()
    else:
        value = get_cell_value(cells)

    # Process specific fields for correct data types
    if field in ["date_of_birth", "marriage_date"]:
        if isinstance(value, datetime):
            value = value.date()
        elif isinstance(value, float) or isinstance(value, int):
            try:
                value = datetime.fromordinal(datetime(1899, 12, 30).toordinal() + int(value)).date()
            except (ValueError, TypeError):
                value = None
        else:
            value = None

    extracted_data[field] = value

    print(f"{field}: {extracted_data[field]}")  # Prints extracted value for verification

# ---------------- Create and Save Django Model Instance ----------------
try:
    profile = Profile(**extracted_data)
    profile.save()
    print("✅ Successfully saved data to the database!")
except Exception as e:
    print(f"❌ An error occurred while saving to the database: {e}")
finally:
    wb.close()