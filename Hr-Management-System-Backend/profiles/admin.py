from django.contrib import admin
from .models import Profile, BankDetails

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = [
        'emp_id', 'get_full_name', 'organization', 'designation', 'department_ref',
        'get_reporting_manager', 'is_reporting_manager', 'country_code', 'phone_number', 'full_phone'
    ]
    list_filter = ['organization', 'department_ref', 'employment_type', 'country_code']
    search_fields = ['emp_id', 'first_name', 'last_name', 'email_id', 'phone_number', 'country_code']
    autocomplete_fields = ['reporting_manager', 'department_ref']
    readonly_fields = ['emp_id', 'created_at', 'updated_at']

    fieldsets = (
        ('User Account', {
            'fields': ('user',)
        }),
        ('Employee Information', {
            'fields': ('emp_id',)
        }),
        ('Personal Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'gender', 'date_of_birth', 'birth_place', 'age', 'nationality', 'religion', 'native_state', 'state_of_domicile', 'marital_status', 'marriage_date', 'passport_photo')
        }),
        ('Contact Information', {
            'fields': (
                'email_id', 'country_code', 'phone_number', 'emergency_contact_number', 'emergency_contact_relationship',
                'present_address', 'present_address_pin_code', 'permanent_address', 'permanent_address_pin_code',
                'city'
            )
        }),
        ('ID & Legal Information', {
            'fields': (
                'aadhar_number', 'pan_number', 'passport_number', 'passport_issue_date', 'passport_valid_upto', 'passport_country_of_issue', 'valid_visa_details',
                'has_criminal_record', 'criminal_record_details'
            )
        }),
        ('Professional Information', {
            'fields': (
                'organization', 'designation', 'department_ref', 'reporting_manager',
                'is_reporting_manager', 'employment_type', 'date_of_joining', 'work_location',
                'previous_company_name', 'years_of_experience', 'highest_qualification', 'college_university_name', 'graduation_year',
                'probation_period_months', 'confirmation_date', 'significant_achievements', 'suitability_for_position',
                'notice_period_days', 'willing_to_relocate', 'willing_to_travel', 'expected_ctc', 'preferred_job_location',
                'how_did_you_know_of_this_position', 'are_you_engaged_in_any_business', 'business_details',
                'any_bond_with_previous_employer', 'bond_details'
            )
        }),
        ('Health Information', {
            'fields': ('height_cm', 'weight_kg', 'blood_group', 'eyesight_right', 'eyesight_left', 'physical_disability', 'identification_marks')
        }),
    )

    def get_form(self, request, obj=None, **kwargs):
        """Customize the form to filter reporting manager choices"""
        form = super().get_form(request, obj, **kwargs)
        if hasattr(form, 'base_fields') and 'reporting_manager' in form.base_fields:
            if obj:
                form.base_fields['reporting_manager'].queryset = Profile.objects.exclude(pk=obj.pk)
            else:
                form.base_fields['reporting_manager'].queryset = Profile.objects.all()
        return form

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}"
    get_full_name.short_description = 'Full Name'

    def get_reporting_manager(self, obj):
        if obj.reporting_manager:
            return f"{obj.reporting_manager.first_name} {obj.reporting_manager.last_name}"
        return "No manager"
    get_reporting_manager.short_description = 'Reports To'

    def is_reporting_manager(self, obj):
        return obj.subordinates.exists()
    is_reporting_manager.short_description = 'Is Manager'
    is_reporting_manager.boolean = True

    def full_phone(self, obj):
        return f"{obj.country_code} {obj.phone_number}"
    full_phone.short_description = 'Full Phone'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'organization', 'department_ref', 'reporting_manager'
        ).prefetch_related('subordinates')

@admin.register(BankDetails)
class BankDetailsAdmin(admin.ModelAdmin):
    list_display = ['profile', 'bank_name', 'bank_account_number', 'ifsc_code']
    search_fields = ['profile__emp_id', 'profile__first_name', 'profile__last_name', 'bank_name']
    autocomplete_fields = ['profile']
