from rest_framework import serializers
from django.db import transaction
from .models import (
    Profile, BankDetails, FamilyMember, LanguageKnown, EducationDetail,
    WorkExperience, Nominee, DocumentSubmission, Declaration, HobbyInterest,
    PreviousCareerRoleDetails, PreviousInterview, RelativeInCompany, BackgroundCheck, VehicleDetail,
    TrainingCertification, PublishedPaper, ExtraCurricularActivity, SkillSummary,
    EmployeeReference, Reference, ProfileUpdateRequest, OnboardingSession, EmployeeOnboardingData
)

from core.models import *

from core.models import Organization, Role, Department, User



# --- Related model serializers ---
class FamilyMemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = FamilyMember
        fields = '__all__'


class LanguageKnownSerializer(serializers.ModelSerializer):
    class Meta:
        model = LanguageKnown
        fields = '__all__'


class EducationDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = EducationDetail
        fields = '__all__'


class WorkExperienceSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkExperience
        fields = '__all__'


class NomineeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Nominee
        fields = '__all__'


class DocumentSubmissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = DocumentSubmission
        fields = '__all__'


class DeclarationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Declaration
        fields = '__all__'


class HobbyInterestSerializer(serializers.ModelSerializer):
    class Meta:
        model = HobbyInterest
        fields = '__all__'


class PreviousCareerRoleDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PreviousCareerRoleDetails
        fields = '__all__'


class PreviousInterviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = PreviousInterview
        fields = '__all__'


class RelativeInCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = RelativeInCompany
        fields = '__all__'


class BackgroundCheckSerializer(serializers.ModelSerializer):
    class Meta:
        model = BackgroundCheck
        fields = '__all__'


class VehicleDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = VehicleDetail
        fields = '__all__'


# --- Profile main serializer with nested onboarding ---
class ProfileSerializer(serializers.ModelSerializer):
    family_members = FamilyMemberSerializer(many=True, required=False)
    languages_known = LanguageKnownSerializer(many=True, required=False)
    education_details = EducationDetailSerializer(many=True, required=False)
    work_experiences = WorkExperienceSerializer(many=True, required=False)
    nominees = NomineeSerializer(many=True, required=False)
    documents_submitted = DocumentSubmissionSerializer(many=True, required=False)
    declarations = DeclarationSerializer(many=True, required=False)
    hobbies = HobbyInterestSerializer(many=True, required=False)
    previous_career_role_details = PreviousCareerRoleDetailsSerializer(required=False)
    previous_interviews = PreviousInterviewSerializer(many=True, required=False)
    relatives_in_company = RelativeInCompanySerializer(many=True, required=False)
    background_checks = BackgroundCheckSerializer(many=True, required=False)
    vehicle_details = VehicleDetailSerializer(many=True, required=False)

    class Meta:
        model = Profile
        fields = '__all__'

    def create(self, validated_data):
        related_fields = {
            'family_members': FamilyMemberSerializer,
            'languages_known': LanguageKnownSerializer,
            'education_details': EducationDetailSerializer,
            'work_experiences': WorkExperienceSerializer,
            'nominees': NomineeSerializer,
            'documents_submitted': DocumentSubmissionSerializer,
            'declarations': DeclarationSerializer,
            'hobbies': HobbyInterestSerializer,
            'previous_interviews': PreviousInterviewSerializer,
            'relatives_in_company': RelativeInCompanySerializer,
            'background_checks': BackgroundCheckSerializer,
            'vehicle_details': VehicleDetailSerializer,
        }

        prev_career_data = validated_data.pop('previous_career_role_details', None)

        with transaction.atomic():
            profile = Profile.objects.create(**validated_data)

            if prev_career_data:
                PreviousCareerRoleDetails.objects.create(profile=profile, **prev_career_data)

            for field, serializer_class in related_fields.items():
                items = validated_data.pop(field, [])
                for item in items:
                    serializer_class().Meta.model.objects.create(profile=profile, **item)

        return profile

    def update(self, instance, validated_data):
        related_fields = {
            'family_members': FamilyMemberSerializer,
            'languages_known': LanguageKnownSerializer,
            'education_details': EducationDetailSerializer,
            'work_experiences': WorkExperienceSerializer,
            'nominees': NomineeSerializer,
            'documents_submitted': DocumentSubmissionSerializer,
            'declarations': DeclarationSerializer,
            'hobbies': HobbyInterestSerializer,
            'previous_interviews': PreviousInterviewSerializer,
            'relatives_in_company': RelativeInCompanySerializer,
            'background_checks': BackgroundCheckSerializer,
            'vehicle_details': VehicleDetailSerializer,
        }

        prev_career_data = validated_data.pop('previous_career_role_details', None)

        with transaction.atomic():
            # update simple fields
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            # update single object
            if prev_career_data:
                PreviousCareerRoleDetails.objects.update_or_create(
                    profile=instance,
                    defaults=prev_career_data
                )

            # update list-based relations (replace-all strategy)
            for field, serializer_class in related_fields.items():
                if field in validated_data:
                    model = serializer_class().Meta.model
                    model.objects.filter(profile=instance).delete()
                    items = validated_data.get(field, [])
                    for item in items:
                        model.objects.create(profile=instance, **item)

        return instance


# --- Other existing model serializers ---
class BankDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankDetails
        fields = '__all__'


class TrainingCertificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrainingCertification
        fields = '__all__'


class PublishedPaperSerializer(serializers.ModelSerializer):
    class Meta:
        model = PublishedPaper
        fields = '__all__'


class ExtraCurricularActivitySerializer(serializers.ModelSerializer):
    class Meta:
        model = ExtraCurricularActivity
        fields = '__all__'


class SkillSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = SkillSummary
        fields = '__all__'


class EmployeeReferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmployeeReference
        fields = '__all__'


class ReferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reference
        fields = '__all__'


class ProfileUpdateRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProfileUpdateRequest
        fields = '__all__'


class OnboardingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = OnboardingSession
        fields = ['id', 'organization', 'status', 'uploaded_file', 'validation_errors', 'created_at']
        read_only_fields = ['created_by', 'validation_errors']


class EmployeeOnboardingDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmployeeOnboardingData
        fields = ['first_name', 'last_name', 'personal_email', 'generated_emp_id', 'company_email']
        read_only_fields = ['generated_emp_id', 'company_email', 'temp_password']


class OnboardingStepOneSerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length=100)
    last_name = serializers.CharField(max_length=100)
    personal_email = serializers.EmailField()
    organization = serializers.PrimaryKeyRelatedField(queryset=Organization.objects.all())


class OnboardingStepThreeSerializer(serializers.Serializer):
    role = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all())
    access_level = serializers.ChoiceField(choices=User.ACCESS_LEVELS)
    department = serializers.PrimaryKeyRelatedField(queryset=Department.objects.all())
    reporting_manager = serializers.PrimaryKeyRelatedField(queryset=Profile.objects.all(), allow_null=True)
    date_of_joining = serializers.DateField()
