from django.db import models

from core.fields import EncryptedField



from django.utils import timezone

# ===================== PROFILE MODEL =====================
class Profile(models.Model):
    class Gender(models.TextChoices):
        MALE = 'M', 'Male'
        FEMALE = 'F', 'Female'
        OTHER = 'O', 'Other'
        NOT_SPECIFIED = 'N', 'Not Specified'

    emp_id = models.CharField(primary_key=True, max_length=20, editable=False)
    user = models.OneToOneField('core.User', on_delete=models.CASCADE, related_name='profile', null=True, blank=True)
    organization = models.ForeignKey('core.Organization', on_delete=models.CASCADE, null=True, blank=True)

    # Personal Info
    first_name = models.Char<PERSON>ield(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.Char<PERSON><PERSON>(max_length=100)
    gender = models.CharField(max_length=1, choices=Gender.choices, default=Gender.NOT_SPECIFIED)
    date_of_birth = models.DateField()
    birth_place = models.CharField(max_length=100, blank=True, null=True)
    age = models.PositiveIntegerField(blank=True, null=True)
    nationality = models.CharField(max_length=50, blank=True, null=True)
    religion = models.CharField(max_length=50, blank=True, null=True)
    native_state = models.CharField(max_length=100, blank=True, null=True)
    state_of_domicile = models.CharField(max_length=100, blank=True, null=True)
    marital_status = models.CharField(max_length=20)
    marriage_date = models.DateField(blank=True, null=True)
    passport_photo = models.ImageField(upload_to='passport_photos/', blank=True, null=True)

    # Contact
    email_id = models.EmailField(unique=True)
    country_code = models.CharField(max_length=5, default='+91')
    phone_number = models.PositiveIntegerField(max_length=15)
    emergency_contact_number = models.CharField(max_length=255)
    emergency_contact_relationship = models.CharField(max_length=50, blank=True, null=True)#N
    present_address = models.CharField(max_length=150)
    present_address_pin_code = models.CharField(max_length=10)
    permanent_address = models.CharField(max_length=150,blank=True, null=True)
    permanent_address_pin_code = models.CharField(max_length=10, blank=True, null=True)
    city = models.CharField(max_length=100)#N
    state = models.CharField(max_length=100, blank = True)#N
    pincode = models.CharField(max_length=10, blank = True)#N
    #landmark = models.CharField(max_length=200, blank=True, null=True)#N
    #years_at_present_address = models.DecimalField(max_digits=4, decimal_places=1, blank=True, null=True)#N

    # IDs
    aadhar_number = models.CharField(max_length=255)
    pan_number = models.CharField(max_length=255)
    passport_number = models.CharField(max_length=20, blank=True, null=True)
    passport_issue_date = models.DateField(blank=True, null=True)
    passport_valid_upto = models.DateField(blank=True, null=True)
    passport_country_of_issue = models.CharField(max_length=50, blank=True, null=True)
    valid_visa_details = models.TextField(blank=True, null=True)

    # Health
    height_cm = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    weight_kg = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    blood_group = models.CharField(max_length=5, blank=True, null=True)
    eyesight_right = models.CharField(max_length=10, blank=True, null=True)
    eyesight_left = models.CharField(max_length=10, blank=True, null=True)
    physical_disability = models.TextField(blank=True, null=True)
    identification_marks = models.TextField(blank=True, null=True)

    # Employment
    designation = models.CharField(max_length=100)
    date_of_joining = models.DateField()
    work_location = models.CharField(max_length=100)
    employment_type = models.CharField(max_length=50)
    previous_company_name = models.CharField(max_length=100, blank=True, null=True)
    years_of_experience = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    highest_qualification = models.CharField(max_length=100)
    college_university_name = models.CharField(max_length=100)
    graduation_year = models.IntegerField(blank=True, null=True)
    is_reporting_manager = models.BooleanField(default=False)
    probation_period_months = models.PositiveIntegerField(blank=True, null=True)
    confirmation_date = models.DateField(blank=True, null=True)

    reporting_manager = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='subordinates')
    department_ref = models.ForeignKey('core.Department', null=True, blank=True, on_delete=models.SET_NULL)

    # Career narrative fields
    significant_achievements = models.TextField(blank=True, null=True)
    suitability_for_position = models.TextField(blank=True, null=True)

    # Work preferences
    notice_period_days = models.PositiveIntegerField(blank=True, null=True)
    willing_to_relocate = models.BooleanField(default=False)
    willing_to_travel = models.BooleanField(default=False)
    expected_ctc = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    preferred_job_location = models.CharField(max_length=150, blank=True, null=True)

    # Legal & compliance
    has_criminal_record = models.BooleanField(default=False)
    criminal_record_details = models.TextField(blank=True, null=True)
    #has_non_compete = models.BooleanField(default=False)
    #non_compete_details = models.TextField(blank=True, null=True)
    #dismissed_before = models.BooleanField(default=False)
    #dismissal_details = models.TextField(blank=True, null=True)

    #Added fields
    how_did_you_know_of_this_position = models.TextField(max_length=150, blank=True, null=True)
    are_you_engaged_in_any_business = models.BooleanField(default=False)
    business_details = models.TextField(blank=True, null=True)
    any_bond_with_previous_employer = models.BooleanField(default=False)
    bond_details = models.TextField(blank=True, null=True)


    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.emp_id:
            last_profile = Profile.objects.order_by('-emp_id').first()
            if last_profile:
                last_num = int(last_profile.emp_id.replace('EMP', ''))
                self.emp_id = f'EMP{last_num + 1:03d}'
            else:
                self.emp_id = 'EMP001'

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.emp_id})"



# ===================== NEW MODELS =====================

class PreviousCareerRoleDetails(models.Model):
    profile = models.OneToOneField(Profile, on_delete=models.CASCADE, related_name='previous_career_role_details')
    description = models.TextField()
    total_persons_under = models.PositiveIntegerField(blank=True, null=True)

class PreviousInterview(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='previous_interviews')
    was_interviewed = models.BooleanField(default=False)
    date_or_year = models.CharField(max_length=20, blank=True, null=True)
    position = models.CharField(max_length=100, blank=True, null=True)
    company = models.CharField(max_length=150, blank=True, null=True)

class RelativeInCompany(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='relatives_in_company')
    name = models.CharField(max_length=100)
    relationship = models.CharField(max_length=50)
    #department = models.CharField(max_length=100, blank=True, null=True)
    designation = models.CharField(max_length=100, blank=True, null=True)
    company = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=10, blank=True, null=True)

class BackgroundCheck(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='background_checks')
    police_verification_done = models.BooleanField(default=False)
    verification_details = models.TextField(blank=True, null=True)

class VehicleDetail(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='vehicle_details')
    vehicle_type = models.CharField(max_length=50)
    registration_number = models.CharField(max_length=20)

class BankDetails(models.Model):
    profile = models.OneToOneField(Profile, on_delete=models.CASCADE, db_column='emp_id')
    organization = models.ForeignKey('core.Organization', on_delete=models.CASCADE)
    bank_account_number = models.CharField(max_length=255)
    bank_name = models.CharField(max_length=100)
    branch_name = models.CharField(max_length=100, blank=True, null=True)
    account_type = models.CharField(max_length=50, blank=True, null=True)
    ifsc_code = models.CharField(max_length=20)
    micr_code = models.CharField(max_length=20, blank=True, null=True)
    swift_code = models.CharField(max_length=20, blank=True, null=True)
    uan_number = models.CharField(max_length=255, blank=True, null=True)
    esic_number = models.CharField(max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class FamilyMember(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='family_members')
    name = models.CharField(max_length=100)
    relationship = models.CharField(max_length=50)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=10, blank=True, null=True)
    qualification = models.CharField(max_length=100)
    occupation = models.CharField(max_length=100)
    organization = models.CharField(max_length=100, blank=True, null=True)
    #contact_number = models.CharField(max_length=20, blank=True, null=True)
    #address = models.TextField(blank=True, null=True)

class LanguageKnown(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='languages_known')
    language = models.CharField(max_length=50)
    can_read = models.BooleanField(default=False)
    can_write = models.BooleanField(default=False)
    can_speak = models.BooleanField(default=False)

class EducationDetail(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='education_details')
    exam_passed = models.CharField(max_length=100)
    specialization = models.CharField(max_length=100, blank=True, null=True)
    institution = models.CharField(max_length=200)
    university_board = models.CharField(max_length=100)
    study_mode = models.CharField(max_length=50, blank=True, null=True)
    duration_years = models.CharField(max_length=20, blank=True, null=True)
    month_year_of_passing = models.CharField(max_length=20)
    grade_or_percentage = models.CharField(max_length=10)
    distinctions = models.TextField(blank=True, null=True)

class WorkExperience(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='work_experiences')
    employer_name = models.CharField(max_length=200)
    duration = models.CharField(max_length=100)
    designation = models.CharField(max_length=100)
    duties = models.TextField(blank=True, null=True)
    from_date = models.DateField()
    to_date = models.DateField()
    superior_name = models.CharField(max_length=100, blank=True, null=True)
    superior_designation = models.CharField(max_length=100, blank=True, null=True)
    salary_at_join = models.CharField(max_length=100, blank=True, null=True)
    lastdrawn_salary = models.CharField(max_length=100, blank=True, null=True)
    emolument_basic = models.CharField(max_length=100, blank=True, null=True)
    emolument_fixed = models.CharField(max_length=100, blank=True, null=True)
    emolument_variable = models.CharField(max_length=100, blank=True, null=True)
    emolument_gross = models.CharField(max_length=100, blank=True, null=True)
    

class Nominee(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='nominees')
    name = models.CharField(max_length=100)
    relationship = models.CharField(max_length=50)
    date_of_birth = models.DateField()
    share_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    address = models.TextField()

class DocumentSubmission(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='documents_submitted')
    document_type = models.CharField(max_length=100)
    document_number = models.CharField(max_length=100, blank=True, null=True)
    issue_date = models.DateField(blank=True, null=True)
    valid_upto = models.DateField(blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)

class Declaration(models.Model):
    DECLARATION_CHOICES = [
        ('not_connected', 'I am not connected with any of the Directors of the Company'),
        ('relative', 'I am a partner or relative of a Director of the Company'),
    ]

    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='declarations')
    declaration_type = models.CharField(max_length=20, choices=DECLARATION_CHOICES)
    #relative_name = models.CharField(max_length=100, blank=True, null=True)  # only if "relative"
    relation = models.CharField(max_length=50, blank=True, null=True)        # e.g., spouse, brother
    director_name = models.CharField(max_length=100, blank=True, null=True)  # optional
    #agreed = models.BooleanField(default=False)
    date_signed = models.DateField(blank=True, null=True)
    signature = models.ImageField(upload_to='signatures/', blank=True, null=True)

    def __str__(self):
        return f"{self.profile.full_name} - {self.get_declaration_type_display()}"


class HobbyInterest(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='hobbies')
    hobby_name = models.CharField(max_length=100)


class TrainingCertification(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='trainings')
    course_name = models.CharField(max_length=200)
    duration = models.CharField(max_length=50)
    year = models.CharField(max_length=10)
    institute = models.CharField(max_length=200)
    certificate_awarded = models.BooleanField(default=False)

class PublishedPaper(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='papers')
    title = models.CharField(max_length=200)
    seminar_or_journal_details = models.TextField()

class ExtraCurricularActivity(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='activities')
    activity_name = models.CharField(max_length=200)
    institution = models.CharField(max_length=200)
    year = models.CharField(max_length=10)
    position_held = models.CharField(max_length=100)
    prizes_won = models.TextField(blank=True, null=True)

class SkillSummary(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='skills')
    project_title = models.CharField(max_length=200)
    role = models.CharField(max_length=100)
    team_size = models.PositiveIntegerField()
    duration = models.CharField(max_length=50)
    technologies = models.CharField(max_length=200)

class EmployeeReference(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='employee_references')
    name = models.CharField(max_length=100)
    company = models.CharField(max_length=200)
    position = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15)
    email = models.EmailField()

class Reference(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='references')
    source = models.CharField(max_length=100)
    details = models.TextField(blank=True, null=True)

class ProfileUpdateRequest(models.Model):
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected')
    ]

    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='update_requests')
    data = models.JSONField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='Pending')
    submitted_at = models.DateTimeField(auto_now_add=True)
    reviewed_by = models.ForeignKey('core.User', on_delete=models.SET_NULL, null=True, blank=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    comments = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Update Request for {self.profile.emp_id} - {self.status}"

    class Meta:
        ordering = ['-submitted_at']

class OnboardingSession(models.Model):
    """Track HR3 onboarding sessions"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('validating', 'Validating'),
        ('validated', 'Validated'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    created_by = models.ForeignKey('core.User', on_delete=models.CASCADE)
    organization = models.ForeignKey('core.Organization', on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    uploaded_file = models.FileField(upload_to='onboarding_files/', null=True, blank=True)
    validation_errors = models.JSONField(default=dict, blank=True)
    employee_data = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class EmployeeOnboardingData(models.Model):
    """Temporary storage for employee data during onboarding"""
    session = models.ForeignKey(OnboardingSession, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    personal_email = models.EmailField()
    generated_emp_id = models.CharField(max_length=20)
    company_email = models.EmailField()
    temp_password = models.CharField(max_length=128)
    is_processed = models.BooleanField(default=False)
    is_processed = models.BooleanField(default=False)
