from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import date, datetime

from ..models import Profile, BankDetails
from core.models import Organization, Department, Role

User = get_user_model()


class ProfileModelTest(TestCase):
    """Test cases for Profile model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.department = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.profile_data = {
            'organization': self.organization,
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'gender': Profile.Gender.MALE,
            'date_of_birth': date(1990, 1, 1),
            'email_id': '<EMAIL>',
            'country_code': '+91',
            'phone_number': '**********',
            'emergency_contact_number': '**********',
            'address': '123 Test Street, Test City',
            'city': 'Test City',
            'state': 'Test State',
            'pincode': '123456',
            'aadhar_number': '************',
            'pan_number': '**********',
            'marital_status': 'Single',
            'designation': 'Software Engineer',
            'date_of_joining': date(2023, 1, 1),
            'work_location': 'Bangalore',
            'employment_type': 'Full-time',
            'previous_company_name': 'Previous Corp',
            'years_of_experience': Decimal('2.5'),
            'highest_qualification': 'B.Tech',
            'college_university_name': 'Test University',
            'graduation_year': 2020,
            'department_ref': self.department,
            'user': self.user
        }

    def test_profile_creation(self):
        """Test basic profile creation"""
        profile = Profile.objects.create(**self.profile_data)
        self.assertIsInstance(profile, Profile)
        self.assertEqual(profile.first_name, 'John')
        self.assertEqual(profile.last_name, 'Doe')
        self.assertEqual(profile.organization, self.organization)
        self.assertEqual(profile.user, self.user)

    def test_profile_str_method(self):
        """Test profile string representation"""
        profile = Profile.objects.create(**self.profile_data)
        expected_str = f"John Doe ({profile.emp_id})"
        self.assertEqual(str(profile), expected_str)

    def test_emp_id_auto_generation(self):
        """Test automatic employee ID generation"""
        profile = Profile.objects.create(**self.profile_data)
        self.assertIsNotNone(profile.emp_id)
        self.assertTrue(profile.emp_id.startswith('EMP'))
        self.assertEqual(len(profile.emp_id), 6)  # EMP + 3 digits

    def test_emp_id_sequential_generation(self):
        """Test sequential employee ID generation"""
        profile1 = Profile.objects.create(**self.profile_data)
        
        # Create second profile with different user
        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        profile_data2 = self.profile_data.copy()
        profile_data2['email_id'] = '<EMAIL>'
        profile_data2['user'] = user2
        profile2 = Profile.objects.create(**profile_data2)
        
        # Extract numeric parts and verify sequence
        num1 = int(profile1.emp_id.replace('EMP', ''))
        num2 = int(profile2.emp_id.replace('EMP', ''))
        self.assertEqual(num2, num1 + 1)

    def test_gender_choices(self):
        """Test gender field choices"""
        # Test all valid gender choices
        for choice_value, choice_label in Profile.Gender.choices:
            profile_data = self.profile_data.copy()
            profile_data['gender'] = choice_value
            profile_data['email_id'] = f'test_{choice_value}@example.com'
            
            # Create new user for each profile
            user = User.objects.create_user(
                email=f'user_{choice_value}@example.com',
                password='testpass123',
                organization=self.organization,
                role=self.role
            )
            profile_data['user'] = user
            
            profile = Profile.objects.create(**profile_data)
            self.assertEqual(profile.gender, choice_value)

    def test_email_unique_constraint(self):
        """Test that email_id must be unique"""
        Profile.objects.create(**self.profile_data)
        
        # Create second profile with same email
        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        profile_data2 = self.profile_data.copy()
        profile_data2['user'] = user2
        
        with self.assertRaises(IntegrityError):
            Profile.objects.create(**profile_data2)

    def test_reporting_manager_relationship(self):
        """Test reporting manager self-referential relationship"""
        # Create manager profile
        manager_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        manager_data = self.profile_data.copy()
        manager_data['email_id'] = '<EMAIL>'
        manager_data['user'] = manager_user
        manager_data['is_reporting_manager'] = True
        manager = Profile.objects.create(**manager_data)
        
        # Create employee with manager
        self.profile_data['reporting_manager'] = manager
        employee = Profile.objects.create(**self.profile_data)
        
        self.assertEqual(employee.reporting_manager, manager)
        self.assertIn(employee, manager.subordinates.all())

    def test_timestamps(self):
        """Test created_at and updated_at timestamps"""
        profile = Profile.objects.create(**self.profile_data)
        self.assertIsNotNone(profile.created_at)
        self.assertIsNotNone(profile.updated_at)
        
        # Test that updated_at changes on save
        original_updated = profile.updated_at
        profile.first_name = 'Jane'
        profile.save()
        self.assertGreater(profile.updated_at, original_updated)

    def test_default_values(self):
        """Test default field values"""
        # Create profile without specifying gender to test default
        profile_data_no_gender = self.profile_data.copy()
        del profile_data_no_gender['gender']  # Remove gender to test default

        profile = Profile.objects.create(**profile_data_no_gender)
        self.assertEqual(profile.gender, Profile.Gender.NOT_SPECIFIED)
        self.assertEqual(profile.country_code, '+91')
        self.assertFalse(profile.is_reporting_manager)

    def test_encrypted_fields(self):
        """Test that encrypted fields store and retrieve data correctly"""
        profile = Profile.objects.create(**self.profile_data)
        
        # Test encrypted fields
        self.assertEqual(profile.phone_number, '**********')
        self.assertEqual(profile.emergency_contact_number, '**********')
        self.assertEqual(profile.address, '123 Test Street, Test City')
        self.assertEqual(profile.aadhar_number, '************')
        self.assertEqual(profile.pan_number, '**********')

    def test_department_relationship(self):
        """Test department foreign key relationship"""
        profile = Profile.objects.create(**self.profile_data)
        self.assertEqual(profile.department_ref, self.department)
        
        # Test cascade behavior when department is deleted
        department_id = self.department.id
        self.department.delete()
        profile.refresh_from_db()
        self.assertIsNone(profile.department_ref)

    def test_organization_cascade_delete(self):
        """Test that profile is deleted when organization is deleted"""
        profile = Profile.objects.create(**self.profile_data)
        profile_id = profile.emp_id
        
        self.organization.delete()
        
        with self.assertRaises(Profile.DoesNotExist):
            Profile.objects.get(emp_id=profile_id)


class BankDetailsModelTest(TestCase):
    """Test cases for BankDetails model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        self.bank_details_data = {
            'profile': self.profile,
            'organization': self.organization,
            'bank_account_number': '****************',
            'bank_name': 'Test Bank',
            'ifsc_code': 'TEST0001234',
            'uan_number': '************',
            'esic_number': '****************7890'
        }

    def test_bank_details_creation(self):
        """Test basic bank details creation"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        self.assertIsInstance(bank_details, BankDetails)
        self.assertEqual(bank_details.profile, self.profile)
        self.assertEqual(bank_details.organization, self.organization)
        self.assertEqual(bank_details.bank_name, 'Test Bank')

    def test_bank_details_str_method(self):
        """Test bank details string representation"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        expected_str = f"Bank Details for {self.profile.emp_id}"
        self.assertEqual(str(bank_details), expected_str)

    def test_one_to_one_relationship_with_profile(self):
        """Test one-to-one relationship with profile"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        
        # Test that profile can access bank details
        self.assertEqual(self.profile.bankdetails, bank_details)
        
        # Test that only one bank details can exist per profile
        with self.assertRaises(IntegrityError):
            BankDetails.objects.create(**self.bank_details_data)

    def test_encrypted_fields_bank_details(self):
        """Test that encrypted fields in bank details work correctly"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        
        # Test encrypted fields
        self.assertEqual(bank_details.bank_account_number, '****************')
        self.assertEqual(bank_details.uan_number, '************')

    def test_timestamps_bank_details(self):
        """Test created_at and updated_at timestamps for bank details"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        self.assertIsNotNone(bank_details.created_at)
        self.assertIsNotNone(bank_details.updated_at)
        
        # Test that updated_at changes on save
        original_updated = bank_details.updated_at
        bank_details.bank_name = 'Updated Bank'
        bank_details.save()
        self.assertGreater(bank_details.updated_at, original_updated)

    def test_cascade_delete_with_profile(self):
        """Test that bank details are deleted when profile is deleted"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        bank_details_id = bank_details.id
        
        self.profile.delete()
        
        with self.assertRaises(BankDetails.DoesNotExist):
            BankDetails.objects.get(id=bank_details_id)

    def test_cascade_delete_with_organization(self):
        """Test that bank details are deleted when organization is deleted"""
        bank_details = BankDetails.objects.create(**self.bank_details_data)
        bank_details_id = bank_details.id
        
        self.organization.delete()
        
        with self.assertRaises(BankDetails.DoesNotExist):
            BankDetails.objects.get(id=bank_details_id)
