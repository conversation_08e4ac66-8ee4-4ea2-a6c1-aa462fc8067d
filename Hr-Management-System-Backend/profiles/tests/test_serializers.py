from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIRequestFactory
from decimal import Decimal
from datetime import date

from ..models import Profile, BankDetails
from ..serializers import ProfileSerializer, BankDetailsSerializer
from core.models import Organization, Department, Role

User = get_user_model()


class ProfileSerializerTest(TestCase):
    """Test cases for ProfileSerializer"""

    def setUp(self):
        """Set up test data"""
        self.factory = APIRequestFactory()
        
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.department = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.profile = Profile.objects.create(
            organization=self.organization,
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            gender=Profile.Gender.MALE,
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            country_code='+91',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street, Test City',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            department_ref=self.department,
            user=self.user
        )
        
        self.valid_profile_data = {
            'organization': self.organization.id,
            'first_name': 'Jane',
            'last_name': 'Smith',
            'gender': Profile.Gender.FEMALE,
            'date_of_birth': '1992-05-15',
            'email_id': '<EMAIL>',
            'country_code': '+1',
            'phone_number': '1234567890',
            'emergency_contact_number': '1234567891',
            'address': '456 Another Street',
            'city': 'Another City',
            'state': 'Another State',
            'pincode': '654321',
            'aadhar_number': '************',
            'pan_number': '**********',
            'marital_status': 'Married',
            'designation': 'Senior Engineer',
            'date_of_joining': '2022-03-01',
            'work_location': 'Mumbai',
            'employment_type': 'Full-time',
            'previous_company_name': 'Old Company',
            'years_of_experience': '5.0',
            'highest_qualification': 'M.Tech',
            'college_university_name': 'Another University',
            'graduation_year': 2018,
            'department_ref': self.department.id,
            'user': self.user.id
        }

    def test_profile_serialization(self):
        """Test serializing a profile"""
        serializer = ProfileSerializer(self.profile)
        data = serializer.data
        
        # Check that all expected fields are present
        expected_fields = [
            'emp_id', 'organization', 'first_name', 'last_name', 'gender',
            'date_of_birth', 'email_id', 'country_code', 'phone_number',
            'emergency_contact_number', 'address', 'city', 'state', 'pincode',
            'aadhar_number', 'pan_number', 'marital_status', 'designation',
            'date_of_joining', 'work_location', 'employment_type',
            'previous_company_name', 'years_of_experience', 'highest_qualification',
            'college_university_name', 'graduation_year', 'reporting_manager',
            'department_ref', 'is_reporting_manager', 'created_at', 'updated_at', 'user'
        ]
        
        for field in expected_fields:
            self.assertIn(field, data)
        
        # Check specific field values
        self.assertEqual(data['first_name'], 'John')
        self.assertEqual(data['last_name'], 'Doe')
        self.assertEqual(data['email_id'], '<EMAIL>')
        self.assertEqual(data['organization'], self.organization.id)

    def test_profile_deserialization_valid_data(self):
        """Test deserializing valid profile data"""
        serializer = ProfileSerializer(data=self.valid_profile_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        
        profile = serializer.save()
        self.assertIsInstance(profile, Profile)
        self.assertEqual(profile.first_name, 'Jane')
        self.assertEqual(profile.last_name, 'Smith')
        self.assertEqual(profile.email_id, '<EMAIL>')

    def test_profile_deserialization_invalid_email(self):
        """Test deserializing profile data with invalid email"""
        invalid_data = self.valid_profile_data.copy()
        invalid_data['email_id'] = 'invalid-email'
        
        serializer = ProfileSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('email_id', serializer.errors)

    def test_profile_deserialization_missing_required_fields(self):
        """Test deserializing profile data with missing required fields"""
        incomplete_data = {
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        serializer = ProfileSerializer(data=incomplete_data)
        self.assertFalse(serializer.is_valid())
        
        # Check that required fields are in errors
        required_fields = ['organization', 'date_of_birth', 'email_id']
        for field in required_fields:
            self.assertIn(field, serializer.errors)

    def test_read_only_fields(self):
        """Test that read-only fields cannot be updated"""
        update_data = {
            'emp_id': 'EMP999',  # Should be read-only
            'first_name': 'Updated Name',
            'created_at': '2020-01-01T00:00:00Z',  # Should be read-only
            'updated_at': '2020-01-01T00:00:00Z'   # Should be read-only
        }
        
        serializer = ProfileSerializer(self.profile, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        updated_profile = serializer.save()
        
        # emp_id should not change (read-only)
        self.assertEqual(updated_profile.emp_id, self.profile.emp_id)
        # first_name should change (not read-only)
        self.assertEqual(updated_profile.first_name, 'Updated Name')

    def test_profile_update(self):
        """Test updating an existing profile"""
        update_data = {
            'first_name': 'Updated John',
            'designation': 'Senior Software Engineer',
            'years_of_experience': '3.5'
        }
        
        serializer = ProfileSerializer(self.profile, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        updated_profile = serializer.save()
        self.assertEqual(updated_profile.first_name, 'Updated John')
        self.assertEqual(updated_profile.designation, 'Senior Software Engineer')
        self.assertEqual(updated_profile.years_of_experience, Decimal('3.5'))

    def test_gender_choices_validation(self):
        """Test gender field choices validation"""
        # Test valid gender choice
        valid_data = self.valid_profile_data.copy()
        valid_data['gender'] = Profile.Gender.OTHER
        serializer = ProfileSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())
        
        # Test invalid gender choice
        invalid_data = self.valid_profile_data.copy()
        invalid_data['gender'] = 'X'  # Invalid choice
        serializer = ProfileSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('gender', serializer.errors)


class BankDetailsSerializerTest(TestCase):
    """Test cases for BankDetailsSerializer"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        self.bank_details = BankDetails.objects.create(
            profile=self.profile,
            organization=self.organization,
            bank_account_number='****************',
            bank_name='Test Bank',
            ifsc_code='TEST0001234',
            uan_number='************',
            esic_number='****************7890'
        )
        
        self.valid_bank_data = {
            'profile': self.profile.emp_id,
            'organization': self.organization.id,
            'bank_account_number': '************7654',
            'bank_name': 'Another Bank',
            'ifsc_code': 'ANOT0005678',
            'uan_number': '************',
            'esic_number': '0************7654321'
        }

    def test_bank_details_serialization(self):
        """Test serializing bank details"""
        serializer = BankDetailsSerializer(self.bank_details)
        data = serializer.data
        
        # Check that all expected fields are present
        expected_fields = [
            'id', 'profile', 'organization', 'bank_account_number',
            'bank_name', 'ifsc_code', 'uan_number', 'esic_number',
            'created_at', 'updated_at'
        ]
        
        for field in expected_fields:
            self.assertIn(field, data)
        
        # Check specific field values
        self.assertEqual(data['bank_name'], 'Test Bank')
        self.assertEqual(data['ifsc_code'], 'TEST0001234')
        self.assertEqual(data['profile'], self.profile.emp_id)

    def test_bank_details_deserialization_valid_data(self):
        """Test deserializing valid bank details data"""
        # Create a new profile for this test
        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        profile2 = Profile.objects.create(
            organization=self.organization,
            first_name='Jane',
            last_name='Smith',
            date_of_birth=date(1992, 5, 15),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='456 Another Street',
            city='Another City',
            state='Another State',
            pincode='654321',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Engineer',
            date_of_joining=date(2023, 2, 1),
            work_location='Mumbai',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('1.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2021,
            user=user2
        )
        
        bank_data = self.valid_bank_data.copy()
        bank_data['profile'] = profile2.emp_id
        
        serializer = BankDetailsSerializer(data=bank_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        
        bank_details = serializer.save()
        self.assertIsInstance(bank_details, BankDetails)
        self.assertEqual(bank_details.bank_name, 'Another Bank')
        self.assertEqual(bank_details.profile, profile2)

    def test_bank_details_deserialization_missing_required_fields(self):
        """Test deserializing bank details data with missing required fields"""
        incomplete_data = {
            'bank_name': 'Test Bank'
        }
        
        serializer = BankDetailsSerializer(data=incomplete_data)
        self.assertFalse(serializer.is_valid())
        
        # Check that required fields are in errors
        required_fields = ['profile', 'organization', 'bank_account_number']
        for field in required_fields:
            self.assertIn(field, serializer.errors)

    def test_read_only_fields_bank_details(self):
        """Test that read-only fields cannot be updated"""
        update_data = {
            'bank_name': 'Updated Bank',
            'created_at': '2020-01-01T00:00:00Z',  # Should be read-only
            'updated_at': '2020-01-01T00:00:00Z'   # Should be read-only
        }
        
        serializer = BankDetailsSerializer(self.bank_details, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        updated_bank_details = serializer.save()
        
        # bank_name should change (not read-only)
        self.assertEqual(updated_bank_details.bank_name, 'Updated Bank')

    def test_bank_details_update(self):
        """Test updating existing bank details"""
        update_data = {
            'bank_name': 'Updated Bank Name',
            'ifsc_code': 'UPDT0009999',
            'esic_number': '99999999999999999999'
        }
        
        serializer = BankDetailsSerializer(self.bank_details, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        updated_bank_details = serializer.save()
        self.assertEqual(updated_bank_details.bank_name, 'Updated Bank Name')
        self.assertEqual(updated_bank_details.ifsc_code, 'UPDT0009999')
        self.assertEqual(updated_bank_details.esic_number, '99999999999999999999')
