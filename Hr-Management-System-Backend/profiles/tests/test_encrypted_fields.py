from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock
from decimal import Decimal
from datetime import date

from ..models import Profile, BankDetails
from core.models import Organization, Role
from core.fields import EncryptedField

User = get_user_model()


class EncryptedFieldTest(TestCase):
    """Test cases for EncryptedField functionality"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )

    def test_encrypted_field_storage_and_retrieval(self):
        """Test that encrypted fields store and retrieve data correctly"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street, Test City',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        # Test that encrypted fields return the original values
        self.assertEqual(profile.phone_number, '**********')
        self.assertEqual(profile.emergency_contact_number, '**********')
        self.assertEqual(profile.address, '123 Test Street, Test City')
        self.assertEqual(profile.aadhar_number, '************')
        self.assertEqual(profile.pan_number, '**********')

    def test_encrypted_field_bank_details(self):
        """Test encrypted fields in bank details"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        bank_details = BankDetails.objects.create(
            profile=profile,
            organization=self.organization,
            bank_account_number='************3456',
            bank_name='Test Bank',
            ifsc_code='TEST0001234',
            uan_number='************',
            esic_number='************34567890'
        )
        
        # Test that encrypted fields return the original values
        self.assertEqual(bank_details.bank_account_number, '************3456')
        self.assertEqual(bank_details.uan_number, '************')

    def test_encrypted_field_with_none_values(self):
        """Test encrypted fields with None values (for fields that allow null)"""
        # Since phone_number doesn't allow null, we'll test with a field that might allow null
        # For this test, we'll create a profile normally and then test the field behavior
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',  # Required field
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )

        # Test that the encrypted field handles the data correctly
        self.assertEqual(profile.phone_number, '**********')

    def test_encrypted_field_with_empty_string(self):
        """Test encrypted fields with empty string values"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='',  # Test empty string
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        # Test that empty strings are handled correctly
        self.assertEqual(profile.phone_number, '')

    def test_encrypted_field_update(self):
        """Test updating encrypted field values"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        # Update encrypted field
        profile.phone_number = '9999888877'
        profile.save()
        
        # Refresh from database and verify
        profile.refresh_from_db()
        self.assertEqual(profile.phone_number, '9999888877')

    @patch('core.fields.ENCRYPTION_AVAILABLE', False)
    def test_encrypted_field_fallback_when_encryption_unavailable(self):
        """Test that encrypted field falls back to regular TextField when encryption is unavailable"""
        # This test simulates the scenario where cryptography package is not available
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        # Even without encryption, data should be stored and retrieved
        self.assertEqual(profile.phone_number, '**********')

    def test_encrypted_field_to_python_method(self):
        """Test the to_python method of EncryptedField"""
        field = EncryptedField()
        
        # Test string input
        self.assertEqual(field.to_python('test_string'), 'test_string')
        
        # Test None input
        self.assertIsNone(field.to_python(None))
        
        # Test non-string input
        self.assertEqual(field.to_python(123), '123')

    def test_encrypted_field_with_special_characters(self):
        """Test encrypted fields with special characters"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street, Apt #5, City@State!',  # Special characters
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        # Test that special characters are handled correctly
        self.assertEqual(profile.address, '123 Test Street, Apt #5, City@State!')

    def test_encrypted_field_with_unicode_characters(self):
        """Test encrypted fields with unicode characters"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street, नई दिल्ली',  # Unicode characters
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        # Test that unicode characters are handled correctly
        self.assertEqual(profile.address, '123 Test Street, नई दिल्ली')

    def test_encrypted_field_data_integrity_after_multiple_saves(self):
        """Test that encrypted field data integrity is maintained after multiple saves"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        original_phone = profile.phone_number
        original_address = profile.address
        
        # Save multiple times without changing encrypted fields
        for i in range(5):
            profile.designation = f'Software Engineer {i}'
            profile.save()
            profile.refresh_from_db()
            
            # Verify encrypted fields remain unchanged
            self.assertEqual(profile.phone_number, original_phone)
            self.assertEqual(profile.address, original_address)
