from django.test import TestCase, RequestFactory
from django.contrib.admin.sites import AdminSite
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import date

from ..models import Profile, BankDetails
from ..admin import ProfileAdmin, BankDetailsAdmin
from core.models import Organization, Department, Role

User = get_user_model()


class MockRequest:
    """Mock request object for admin tests"""
    def __init__(self, user=None):
        self.user = user


class ProfileAdminTest(TestCase):
    """Test cases for ProfileAdmin"""

    def setUp(self):
        """Set up test data"""
        self.site = AdminSite()
        self.admin = ProfileAdmin(Profile, self.site)
        self.factory = RequestFactory()
        
        # Create test data
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.department = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.manager_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.manager_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Jane',
            last_name='Manager',
            date_of_birth=date(1985, 5, 15),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='9876543212',
            address='456 Manager Street',
            city='Manager City',
            state='Manager State',
            pincode='654321',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Married',
            designation='Team Lead',
            date_of_joining=date(2020, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('5.0'),
            highest_qualification='M.Tech',
            college_university_name='Manager University',
            graduation_year=2018,
            department_ref=self.department,
            is_reporting_manager=True,
            user=self.manager_user
        )
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            gender=Profile.Gender.MALE,
            date_of_birth=date(1990, 1, 1),
                self.user = User.objects.create_user(
                    email='<EMAIL>',
                    password='testpass123',
                    organization=self.organization,
                    role=self.role
            email_id='<EMAIL>',
            country_code='+91',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street, Test City',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            department_ref=self.department,
            reporting_manager=self.manager_profile,
            user=self.user
        )

    def test_list_display_fields(self):
        """Test that list_display contains expected fields"""
        expected_fields = [
            'emp_id', 'get_full_name', 'organization', 'designation', 'department_ref',
            'get_reporting_manager', 'is_reporting_manager', 'country_code', 'phone_number', 'full_phone'
        ]
        
        for field in expected_fields:
            self.assertIn(field, self.admin.list_display)

    def test_list_filter_fields(self):
        """Test that list_filter contains expected fields"""
        expected_filters = ['organization', 'department_ref', 'employment_type', 'country_code']
        
        for filter_field in expected_filters:
            self.assertIn(filter_field, self.admin.list_filter)

    def test_search_fields(self):
        """Test that search_fields contains expected fields"""
        expected_search_fields = ['emp_id', 'first_name', 'last_name', 'email_id', 'phone_number', 'country_code']
        
        for field in expected_search_fields:
            self.assertIn(field, self.admin.search_fields)

    def test_readonly_fields(self):
        """Test that readonly_fields contains expected fields"""
        expected_readonly = ['emp_id', 'created_at', 'updated_at']
        
        for field in expected_readonly:
            self.assertIn(field, self.admin.readonly_fields)

    def test_autocomplete_fields(self):
        """Test that autocomplete_fields contains expected fields"""
        expected_autocomplete = ['reporting_manager', 'department_ref']
        
        for field in expected_autocomplete:
            self.assertIn(field, self.admin.autocomplete_fields)

    def test_get_full_name_method(self):
        """Test get_full_name admin method"""
        full_name = self.admin.get_full_name(self.profile)
        self.assertEqual(full_name, 'John Doe')

    def test_get_reporting_manager_method(self):
        """Test get_reporting_manager admin method"""
        manager_name = self.admin.get_reporting_manager(self.profile)
        self.assertEqual(manager_name, 'Jane Manager')
        
        # Test profile without reporting manager
        self.profile.reporting_manager = None
        manager_name = self.admin.get_reporting_manager(self.profile)
        self.assertEqual(manager_name, '-')

    def test_full_phone_method(self):
        """Test full_phone admin method"""
        full_phone = self.admin.full_phone(self.profile)
        self.assertEqual(full_phone, '+91 **********')

    def test_get_queryset_optimization(self):
        """Test that get_queryset includes select_related optimization"""
        request = MockRequest()
        queryset = self.admin.get_queryset(request)
        
        # Check that the queryset is optimized with select_related
        self.assertIn('organization', str(queryset.query))
        self.assertIn('department_ref', str(queryset.query))

    def test_admin_methods_short_description(self):
        """Test that admin methods have proper short descriptions"""
        self.assertEqual(self.admin.get_full_name.short_description, 'Full Name')
        self.assertEqual(self.admin.get_reporting_manager.short_description, 'Reporting Manager')
        self.assertEqual(self.admin.full_phone.short_description, 'Full Phone')

    def test_admin_methods_admin_order_field(self):
        """Test that admin methods have proper admin_order_field"""
        self.assertEqual(self.admin.get_full_name.admin_order_field, 'first_name')
        self.assertEqual(self.admin.get_reporting_manager.admin_order_field, 'reporting_manager')


class BankDetailsAdminTest(TestCase):
    """Test cases for BankDetailsAdmin"""

    def setUp(self):
        """Set up test data"""
        self.site = AdminSite()
        self.admin = BankDetailsAdmin(BankDetails, self.site)
        
        # Create test data
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.role
        )
        
        self.profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.user
        )
        
        self.bank_details = BankDetails.objects.create(
            profile=self.profile,
            organization=self.organization,
            bank_account_number='************3456',
            bank_name='Test Bank',
            ifsc_code='TEST0001234',
            uan_number='************',
            esic_number='************34567890'
        )

    def test_list_display_fields(self):
        """Test that list_display contains expected fields"""
        expected_fields = ['profile', 'bank_name', 'bank_account_number', 'ifsc_code']
        
        for field in expected_fields:
            self.assertIn(field, self.admin.list_display)

    def test_search_fields(self):
        """Test that search_fields contains expected fields"""
        expected_search_fields = ['profile__emp_id', 'profile__first_name', 'profile__last_name', 'bank_name']
        
        for field in expected_search_fields:
            self.assertIn(field, self.admin.search_fields)

    def test_autocomplete_fields(self):
        """Test that autocomplete_fields contains expected fields"""
        expected_autocomplete = ['profile']
        
        for field in expected_autocomplete:
            self.assertIn(field, self.admin.autocomplete_fields)

    def test_get_queryset_optimization(self):
        """Test that get_queryset includes select_related optimization"""
        request = MockRequest()
        queryset = self.admin.get_queryset(request)
        
        # Check that the queryset is optimized with select_related
        self.assertIn('profile', str(queryset.query))

    def test_bank_details_str_representation_in_admin(self):
        """Test that bank details string representation works in admin context"""
        str_repr = str(self.bank_details)
        self.assertEqual(str_repr, f"Bank Details for {self.profile.emp_id}")

    def test_admin_integration_with_profile(self):
        """Test that admin properly integrates with profile relationship"""
        # Test that we can access profile information through bank details
        self.assertEqual(self.bank_details.profile.first_name, 'John')
        self.assertEqual(self.bank_details.profile.last_name, 'Doe')
        self.assertEqual(self.bank_details.profile.emp_id, self.profile.emp_id)
