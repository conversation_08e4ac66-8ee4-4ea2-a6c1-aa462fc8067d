from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch
from decimal import Decimal
from datetime import date

from ..models import Profile, BankDetails
from ..views import ProfileViewSet, BankDetailsViewSet
from core.models import Organization, Department, Role

User = get_user_model()


class ProfileViewSetTest(APITestCase):
    """Test cases for ProfileViewSet"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create organizations
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )
        
        self.other_organization = Organization.objects.create(
            company_name="Other Company",
            industry_type="Finance"
        )
        
        # Create departments
        self.department = Department.objects.create(
            organization=self.organization,
            department_name="Engineering",
            description="Software Engineering Department"
        )
        
        # Create roles
        self.hr_role = Role.objects.create(
            organization=self.organization,
            role_name="HR",
            description="Human Resources"
        )
        
        self.admin_role = Role.objects.create(
            organization=self.organization,
            role_name="Admin",
            description="Administrator"
        )
        
        self.employee_role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )
        
        # Create users
        self.hr_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.hr_role,
            access_level='Hr1'
        )
        
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.admin_role,
            is_superuser=True
        )
        
        self.employee_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role,
            access_level='Emp1'
        )
        
        self.manager_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role,
            access_level='Emp2'
        )
        
        # Create profiles
        self.employee_profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='9876543210',
            emergency_contact_number='9876543211',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            department_ref=self.department,
            user=self.employee_user
        )
        
        self.manager_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Jane',
            last_name='Manager',
            date_of_birth=date(1985, 5, 15),
            email_id='<EMAIL>',
            phone_number='9876543211',
            emergency_contact_number='9876543212',
            address='456 Manager Street',
            city='Manager City',
            state='Manager State',
            pincode='654321',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Married',
            designation='Team Lead',
            date_of_joining=date(2020, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('5.0'),
            highest_qualification='M.Tech',
            college_university_name='Manager University',
            graduation_year=2018,
            department_ref=self.department,
            is_reporting_manager=True,
            user=self.manager_user
        )
        
        # Set reporting relationship
        self.employee_profile.reporting_manager = self.manager_profile
        self.employee_profile.save()
        
        self.profile_data = {
            'organization': self.organization.id,
            'first_name': 'New',
            'last_name': 'Employee',
            'gender': Profile.Gender.FEMALE,
            'date_of_birth': '1995-03-20',
            'email_id': '<EMAIL>',
            'country_code': '+91',
            'phone_number': '**********',
            'emergency_contact_number': '**********',
            'address': '789 New Street',
            'city': 'New City',
            'state': 'New State',
            'pincode': '789012',
            'aadhar_number': '************',
            'pan_number': '**********',
            'marital_status': 'Single',
            'designation': 'Junior Engineer',
            'date_of_joining': '2023-06-01',
            'work_location': 'Mumbai',
            'employment_type': 'Full-time',
            'previous_company_name': 'Fresh Graduate',
            'years_of_experience': '0.0',
            'highest_qualification': 'B.Tech',
            'college_university_name': 'New University',
            'graduation_year': 2023,
            'department_ref': self.department.id,
            'user': self.employee_user.id
        }

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_profiles_as_hr(self):
        """Test listing profiles as HR user"""
        self._authenticate_user(self.hr_user)

        url = reverse('profiles-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check if response is paginated
        if 'results' in response.data:
            self.assertGreaterEqual(len(response.data['results']), 2)  # Should see all profiles
        else:
            self.assertGreaterEqual(len(response.data), 2)  # Should see all profiles

    def test_list_profiles_as_admin(self):
        """Test listing profiles as admin user"""
        self._authenticate_user(self.admin_user)

        url = reverse('profiles-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check if response is paginated
        if 'results' in response.data:
            self.assertGreaterEqual(len(response.data['results']), 2)  # Should see all profiles
        else:
            self.assertGreaterEqual(len(response.data), 2)  # Should see all profiles

    def test_list_profiles_as_employee_own_only(self):
        """Test listing profiles as employee (should see only own profile)"""
        self._authenticate_user(self.employee_user)

        url = reverse('profiles-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check if response is paginated
        if 'results' in response.data:
            self.assertEqual(len(response.data['results']), 1)  # Should see only own profile
            self.assertEqual(response.data['results'][0]['emp_id'], self.employee_profile.emp_id)
        else:
            self.assertEqual(len(response.data), 1)  # Should see only own profile
            self.assertEqual(response.data[0]['emp_id'], self.employee_profile.emp_id)

    def test_list_profiles_as_manager_team_members(self):
        """Test listing profiles as manager (should see team members)"""
        self._authenticate_user(self.manager_user)

        url = reverse('profiles-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check if response is paginated
        if 'results' in response.data:
            self.assertEqual(len(response.data['results']), 1)  # Should see reportees
            self.assertEqual(response.data['results'][0]['emp_id'], self.employee_profile.emp_id)
        else:
            self.assertEqual(len(response.data), 1)  # Should see reportees
            self.assertEqual(response.data[0]['emp_id'], self.employee_profile.emp_id)

    def test_list_profiles_unauthenticated(self):
        """Test listing profiles without authentication"""
        url = reverse('profiles-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_profile_as_hr(self):
        """Test creating profile as HR user"""
        self._authenticate_user(self.hr_user)
        
        # Create a new user for the profile
        new_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role
        )
        
        profile_data = self.profile_data.copy()
        profile_data['user'] = new_user.id
        profile_data['email_id'] = '<EMAIL>'
        
        url = reverse('profiles-list')
        response = self.client.post(url, profile_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['first_name'], 'New')
        self.assertEqual(response.data['last_name'], 'Employee')

    def test_create_profile_as_employee_forbidden(self):
        """Test creating profile as employee (should be forbidden)"""
        self._authenticate_user(self.employee_user)
        
        url = reverse('profiles-list')
        response = self.client.post(url, self.profile_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_profile_as_hr(self):
        """Test updating profile as HR user"""
        self._authenticate_user(self.hr_user)
        
        url = reverse('profiles-detail', kwargs={'pk': self.employee_profile.emp_id})
        update_data = {
            'first_name': 'Updated John',
            'designation': 'Senior Software Engineer'
        }
        response = self.client.patch(url, update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated John')
        self.assertEqual(response.data['designation'], 'Senior Software Engineer')

    def test_update_profile_as_employee_forbidden(self):
        """Test updating profile as employee (should be forbidden)"""
        self._authenticate_user(self.employee_user)
        
        url = reverse('profiles-detail', kwargs={'pk': self.employee_profile.emp_id})
        update_data = {'first_name': 'Updated John'}
        response = self.client.patch(url, update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_profile_as_hr(self):
        """Test deleting profile as HR user"""
        self._authenticate_user(self.hr_user)
        
        url = reverse('profiles-detail', kwargs={'pk': self.employee_profile.emp_id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Profile.objects.filter(emp_id=self.employee_profile.emp_id).exists())

    def test_delete_profile_as_employee_forbidden(self):
        """Test deleting profile as employee (should be forbidden)"""
        self._authenticate_user(self.employee_user)
        
        url = reverse('profiles-detail', kwargs={'pk': self.employee_profile.emp_id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_me_endpoint_authenticated_user(self):
        """Test /me endpoint for authenticated user with profile"""
        self._authenticate_user(self.employee_user)
        
        url = reverse('profiles-me')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['emp_id'], self.employee_profile.emp_id)
        self.assertEqual(response.data['first_name'], 'John')

    def test_me_endpoint_user_without_profile(self):
        """Test /me endpoint for user without profile"""
        # Create user without profile
        user_without_profile = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role
        )
        
        self._authenticate_user(user_without_profile)
        
        url = reverse('profiles-me')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('No profile associated with user', response.data['error'])

    def test_me_endpoint_unauthenticated(self):
        """Test /me endpoint without authentication"""
        url = reverse('profiles-me')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch('profiles.views.role_required')
    def test_role_required_decorator_applied(self, mock_role_required):
        """Test that role_required decorator is applied to CRUD operations"""
        # This test verifies that the decorator is applied
        # The actual functionality is tested in other methods
        mock_role_required.return_value = lambda func: func
        
        viewset = ProfileViewSet()
        
        # Check that methods have the decorator applied
        self.assertTrue(hasattr(viewset.create, '__wrapped__'))
        self.assertTrue(hasattr(viewset.update, '__wrapped__'))
        self.assertTrue(hasattr(viewset.partial_update, '__wrapped__'))
        self.assertTrue(hasattr(viewset.destroy, '__wrapped__'))

    def test_swagger_fake_view_handling(self):
        """Test that swagger fake view returns empty queryset"""
        viewset = ProfileViewSet()
        viewset.swagger_fake_view = True
        viewset.request = type('MockRequest', (), {'user': self.employee_user})()
        
        queryset = viewset.get_queryset()
        self.assertEqual(queryset.count(), 0)

    def test_access_level_emp3_department_view(self):
        """Test access level Emp3 can see department-level profiles"""
        # Create department head user
        dept_head_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role,
            access_level='Emp3'
        )
        
        dept_head_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Dept',
            last_name='Head',
            date_of_birth=date(1980, 1, 1),
            email_id='<EMAIL>',
            phone_number='9876543215',
            emergency_contact_number='9876543216',
            address='Department Head Street',
            city='Dept City',
            state='Dept State',
            pincode='111111',
            aadhar_number='111111111111',
            pan_number='**********',
            marital_status='Married',
            designation='Department Head',
            date_of_joining=date(2015, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('8.0'),
            highest_qualification='MBA',
            college_university_name='Business School',
            graduation_year=2015,
            department_ref=self.department,
            user=dept_head_user
        )
        
        self._authenticate_user(dept_head_user)
        
        url = reverse('profiles-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should see all profiles in the same department
        dept_profiles = [p for p in response.data if p.get('department_ref') == self.department.id]
        self.assertGreaterEqual(len(dept_profiles), 2)  # At least employee and manager profiles


class BankDetailsViewSetTest(APITestCase):
    """Test cases for BankDetailsViewSet"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        # Create organization
        self.organization = Organization.objects.create(
            company_name="Test Company",
            industry_type="Technology"
        )

        # Create roles
        self.hr_role = Role.objects.create(
            organization=self.organization,
            role_name="HR",
            description="Human Resources"
        )

        self.employee_role = Role.objects.create(
            organization=self.organization,
            role_name="Employee",
            description="Regular Employee"
        )

        # Create users
        self.hr_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.hr_role
        )

        self.employee_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role
        )

        self.other_employee_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role
        )

        # Create profiles
        self.employee_profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='9876543210',
            emergency_contact_number='9876543211',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Bangalore',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('2.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2020,
            user=self.employee_user
        )

        self.other_employee_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Jane',
            last_name='Smith',
            date_of_birth=date(1992, 5, 15),
            email_id='<EMAIL>',
            phone_number='9876543211',
            emergency_contact_number='9876543212',
            address='456 Other Street',
            city='Other City',
            state='Other State',
            pincode='654321',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Engineer',
            date_of_joining=date(2023, 2, 1),
            work_location='Mumbai',
            employment_type='Full-time',
            previous_company_name='Previous Corp',
            years_of_experience=Decimal('1.5'),
            highest_qualification='B.Tech',
            college_university_name='Test University',
            graduation_year=2021,
            user=self.other_employee_user
        )

        # Create bank details
        self.employee_bank_details = BankDetails.objects.create(
            profile=self.employee_profile,
            organization=self.organization,
            bank_account_number='****************',
            bank_name='Test Bank',
            ifsc_code='TEST0001234',
            uan_number='************',
            esic_number='123456************90'
        )

        self.other_bank_details = BankDetails.objects.create(
            profile=self.other_employee_profile,
            organization=self.organization,
            bank_account_number='************7654',
            bank_name='Other Bank',
            ifsc_code='OTHR0005678',
            uan_number='************',
            esic_number='0************7654321'
        )

        self.bank_data = {
            'profile': self.employee_profile.emp_id,
            'organization': self.organization.id,
            'bank_account_number': '************8888',
            'bank_name': 'New Bank',
            'ifsc_code': 'NEWB0009999',
            'uan_number': '************',
            'esic_number': '************88889999'
        }

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_bank_details_as_hr(self):
        """Test listing bank details as HR user"""
        self._authenticate_user(self.hr_user)

        url = reverse('bank-details-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Should see all bank details

    def test_list_bank_details_as_employee_own_only(self):
        """Test listing bank details as employee (should see only own)"""
        self._authenticate_user(self.employee_user)

        url = reverse('bank-details-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)  # Should see only own bank details
        self.assertEqual(response.data[0]['profile'], self.employee_profile.emp_id)

    def test_list_bank_details_unauthenticated(self):
        """Test listing bank details without authentication"""
        url = reverse('bank-details-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_bank_details_as_hr(self):
        """Test creating bank details as HR user"""
        self._authenticate_user(self.hr_user)

        # Create a new profile without bank details
        new_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            organization=self.organization,
            role=self.employee_role
        )

        new_profile = Profile.objects.create(
            organization=self.organization,
            first_name='New',
            last_name='User',
            date_of_birth=date(1995, 3, 20),
            email_id='<EMAIL>',
            phone_number='**********',
            emergency_contact_number='**********',
            address='789 New Street',
            city='New City',
            state='New State',
            pincode='789012',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Single',
            designation='Junior Engineer',
            date_of_joining=date(2023, 6, 1),
            work_location='Mumbai',
            employment_type='Full-time',
            previous_company_name='Fresh Graduate',
            years_of_experience=Decimal('0.0'),
            highest_qualification='B.Tech',
            college_university_name='New University',
            graduation_year=2023,
            user=new_user
        )

        bank_data = self.bank_data.copy()
        bank_data['profile'] = new_profile.emp_id

        url = reverse('bank-details-list')
        response = self.client.post(url, bank_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['bank_name'], 'New Bank')
        self.assertEqual(response.data['profile'], new_profile.emp_id)

    def test_create_bank_details_as_employee_forbidden(self):
        """Test creating bank details as employee (should be forbidden)"""
        self._authenticate_user(self.employee_user)

        url = reverse('bank-details-list')
        response = self.client.post(url, self.bank_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_bank_details_as_hr(self):
        """Test updating bank details as HR user"""
        self._authenticate_user(self.hr_user)

        url = reverse('bank-details-detail', kwargs={'pk': self.employee_bank_details.id})
        update_data = {
            'bank_name': 'Updated Bank',
            'ifsc_code': 'UPDT0009999'
        }
        response = self.client.patch(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['bank_name'], 'Updated Bank')
        self.assertEqual(response.data['ifsc_code'], 'UPDT0009999')

    def test_update_bank_details_as_employee_forbidden(self):
        """Test updating bank details as employee (should be forbidden)"""
        self._authenticate_user(self.employee_user)

        url = reverse('bank-details-detail', kwargs={'pk': self.employee_bank_details.id})
        update_data = {'bank_name': 'Updated Bank'}
        response = self.client.patch(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_bank_details_as_hr(self):
        """Test deleting bank details as HR user"""
        self._authenticate_user(self.hr_user)

        url = reverse('bank-details-detail', kwargs={'pk': self.employee_bank_details.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(BankDetails.objects.filter(id=self.employee_bank_details.id).exists())

    def test_delete_bank_details_as_employee_forbidden(self):
        """Test deleting bank details as employee (should be forbidden)"""
        self._authenticate_user(self.employee_user)

        url = reverse('bank-details-detail', kwargs={'pk': self.employee_bank_details.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_bank_details_as_owner(self):
        """Test retrieving bank details as the owner"""
        self._authenticate_user(self.employee_user)

        url = reverse('bank-details-detail', kwargs={'pk': self.employee_bank_details.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['profile'], self.employee_profile.emp_id)

    def test_retrieve_bank_details_as_other_employee_forbidden(self):
        """Test retrieving other employee's bank details (should be forbidden)"""
        self._authenticate_user(self.other_employee_user)

        url = reverse('bank-details-detail', kwargs={'pk': self.employee_bank_details.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_swagger_fake_view_handling_bank_details(self):
        """Test that swagger fake view returns empty queryset for bank details"""
        viewset = BankDetailsViewSet()
        viewset.swagger_fake_view = True
        viewset.request = type('MockRequest', (), {'user': self.employee_user})()

        queryset = viewset.get_queryset()
        self.assertEqual(queryset.count(), 0)

    @patch('profiles.views.role_required')
    def test_role_required_decorator_applied_bank_details(self, mock_role_required):
        """Test that role_required decorator is applied to bank details CRUD operations"""
        mock_role_required.return_value = lambda func: func

        viewset = BankDetailsViewSet()

        # Check that methods have the decorator applied
        self.assertTrue(hasattr(viewset.create, '__wrapped__'))
        self.assertTrue(hasattr(viewset.update, '__wrapped__'))
        self.assertTrue(hasattr(viewset.partial_update, '__wrapped__'))
        self.assertTrue(hasattr(viewset.destroy, '__wrapped__'))
